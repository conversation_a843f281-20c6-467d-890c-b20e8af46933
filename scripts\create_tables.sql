-- 创建角色权限系统所需的数据库表

-- 1. 创建用户角色表
CREATE TABLE IF NOT EXISTS `user_roles` (
    `id` bigint NOT NULL AUTO_INCREMENT,
    `name` varchar(20) NOT NULL UNIQUE,
    `display_name` varchar(50) NOT NULL,
    `description` longtext NOT NULL,
    `permissions` json NOT NULL,
    `created_at` datetime(6) NOT NULL,
    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 2. 创建扩展用户表
CREATE TABLE IF NOT EXISTS `users` (
    `id` bigint NOT NULL AUTO_INCREMENT,
    `password` varchar(128) NOT NULL,
    `last_login` datetime(6) DEFAULT NULL,
    `is_superuser` tinyint(1) NOT NULL,
    `username` varchar(150) NOT NULL UNIQUE,
    `first_name` varchar(150) NOT NULL,
    `last_name` varchar(150) NOT NULL,
    `email` varchar(254) NOT NULL,
    `is_staff` tinyint(1) NOT NULL,
    `is_active` tinyint(1) NOT NULL,
    `date_joined` datetime(6) NOT NULL,
    `phone` varchar(20) NOT NULL,
    `department` varchar(100) NOT NULL,
    `last_login_ip` char(39) DEFAULT NULL,
    `created_at` datetime(6) NOT NULL,
    `updated_at` datetime(6) NOT NULL,
    `role_id` bigint DEFAULT NULL,
    PRIMARY KEY (`id`),
    KEY `users_role_id_fk` (`role_id`),
    CONSTRAINT `users_role_id_fk` FOREIGN KEY (`role_id`) REFERENCES `user_roles` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 3. 创建用户组关联表
CREATE TABLE IF NOT EXISTS `users_groups` (
    `id` bigint NOT NULL AUTO_INCREMENT,
    `customuser_id` bigint NOT NULL,
    `group_id` int NOT NULL,
    PRIMARY KEY (`id`),
    UNIQUE KEY `users_groups_customuser_id_group_id_unique` (`customuser_id`, `group_id`),
    KEY `users_groups_group_id_fk` (`group_id`),
    CONSTRAINT `users_groups_customuser_id_fk` FOREIGN KEY (`customuser_id`) REFERENCES `users` (`id`) ON DELETE CASCADE,
    CONSTRAINT `users_groups_group_id_fk` FOREIGN KEY (`group_id`) REFERENCES `auth_group` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 4. 创建用户权限关联表
CREATE TABLE IF NOT EXISTS `users_user_permissions` (
    `id` bigint NOT NULL AUTO_INCREMENT,
    `customuser_id` bigint NOT NULL,
    `permission_id` int NOT NULL,
    PRIMARY KEY (`id`),
    UNIQUE KEY `users_user_permissions_customuser_id_permission_id_unique` (`customuser_id`, `permission_id`),
    KEY `users_user_permissions_permission_id_fk` (`permission_id`),
    CONSTRAINT `users_user_permissions_customuser_id_fk` FOREIGN KEY (`customuser_id`) REFERENCES `users` (`id`) ON DELETE CASCADE,
    CONSTRAINT `users_user_permissions_permission_id_fk` FOREIGN KEY (`permission_id`) REFERENCES `auth_permission` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 5. 创建用户登录日志表
CREATE TABLE IF NOT EXISTS `user_login_logs` (
    `id` bigint NOT NULL AUTO_INCREMENT,
    `login_time` datetime(6) NOT NULL,
    `ip_address` char(39) NOT NULL,
    `user_agent` longtext NOT NULL,
    `login_result` tinyint(1) NOT NULL,
    `user_id` bigint NOT NULL,
    PRIMARY KEY (`id`),
    KEY `user_login_logs_user_id_fk` (`user_id`),
    KEY `user_login_logs_login_time_idx` (`login_time`),
    CONSTRAINT `user_login_logs_user_id_fk` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 6. 插入默认角色数据
INSERT IGNORE INTO `user_roles` (`name`, `display_name`, `description`, `permissions`, `created_at`) VALUES
('admin', '管理员', '系统管理员，拥有所有权限，可以管理用户、角色和系统配置', 
 '{"view_dashboard": true, "view_basic_analysis": true, "view_advanced_analysis": true, "view_prediction": true, "view_comparison": true, "export_data": true, "manage_users": true, "manage_roles": true, "manage_data": true, "view_logs": true, "system_config": true}', 
 NOW()),
('analyst', '数据分析师', '数据分析师，拥有高级分析权限，可以进行预测分析和数据对比', 
 '{"view_dashboard": true, "view_basic_analysis": true, "view_advanced_analysis": true, "view_prediction": true, "view_comparison": true, "export_data": true, "manage_users": false, "manage_roles": false, "manage_data": false, "view_logs": false, "system_config": false}', 
 NOW()),
('user', '普通用户', '普通用户，只能查看基础分析功能', 
 '{"view_dashboard": true, "view_basic_analysis": true, "view_advanced_analysis": false, "view_prediction": false, "view_comparison": false, "export_data": false, "manage_users": false, "manage_roles": false, "manage_data": false, "view_logs": false, "system_config": false}', 
 NOW());

-- 7. 插入默认用户数据（密码已经过Django的pbkdf2_sha256加密）
-- 密码分别是：admin123, analyst123, user123
INSERT IGNORE INTO `users` (
    `username`, `password`, `email`, `first_name`, `last_name`, 
    `is_superuser`, `is_staff`, `is_active`, `date_joined`, 
    `phone`, `department`, `created_at`, `updated_at`, `role_id`
) VALUES
('admin', 'pbkdf2_sha256$260000$placeholder$hash', '<EMAIL>', '系统', '管理员', 
 1, 1, 1, NOW(), '', '系统管理部', NOW(), NOW(), 
 (SELECT id FROM user_roles WHERE name = 'admin')),
('analyst', 'pbkdf2_sha256$260000$placeholder$hash', '<EMAIL>', '数据', '分析师', 
 0, 0, 1, NOW(), '', '数据分析部', NOW(), NOW(), 
 (SELECT id FROM user_roles WHERE name = 'analyst')),
('user', 'pbkdf2_sha256$260000$placeholder$hash', '<EMAIL>', '普通', '用户', 
 0, 0, 1, NOW(), '', '业务部门', NOW(), NOW(), 
 (SELECT id FROM user_roles WHERE name = 'user'));

-- 8. 更新django_migrations表，标记迁移已完成
INSERT IGNORE INTO `django_migrations` (`app`, `name`, `applied`) VALUES
('analysis', '0001_initial', NOW());

COMMIT;
