{% extends "base.html" %}

{% block title %}手机筛选器 - 手机数据分析平台{% endblock %}

{% block extra_css %}
<style>
        .filter-container {
            width: 100%;
            padding: 20px;
        }
        
        .filter-panel {
            background: #fff;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .filter-row {
            display: flex;
            flex-wrap: wrap;
            gap: 15px;
            margin-bottom: 15px;
        }
        
        .filter-group {
            flex: 1;
            min-width: 200px;
        }
        
        .filter-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #333;
        }
        
        .filter-group select,
        .filter-group input {
            width: 100%;
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
        }
        
        .price-range {
            display: flex;
            gap: 10px;
            align-items: center;
        }
        
        .price-range input {
            flex: 1;
        }
        
        .filter-actions {
            display: flex;
            gap: 10px;
            justify-content: flex-end;
            margin-top: 20px;
        }
        
        .btn {
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            transition: background-color 0.3s;
        }
        
        .btn-primary {
            background-color: #007bff;
            color: white;
        }
        
        .btn-primary:hover {
            background-color: #0056b3;
        }
        
        .btn-secondary {
            background-color: #6c757d;
            color: white;
        }
        
        .btn-secondary:hover {
            background-color: #545b62;
        }
        
        .results-container {
            background: #fff;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .results-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 1px solid #eee;
        }
        
        .results-stats {
            display: flex;
            gap: 20px;
            font-size: 14px;
            color: #666;
        }
        
        .phone-table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
        }
        
        .phone-table th,
        .phone-table td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #eee;
        }
        
        .phone-table th {
            background-color: #f8f9fa;
            font-weight: bold;
            cursor: pointer;
        }
        
        .phone-table th:hover {
            background-color: #e9ecef;
        }
        
        .phone-table tr:hover {
            background-color: #f8f9fa;
        }
        
        .pagination {
            display: flex;
            justify-content: center;
            gap: 5px;
        }
        
        .pagination button {
            padding: 8px 12px;
            border: 1px solid #ddd;
            background: white;
            cursor: pointer;
            border-radius: 4px;
        }
        
        .pagination button:hover {
            background-color: #f8f9fa;
        }
        
        .pagination button.active {
            background-color: #007bff;
            color: white;
            border-color: #007bff;
        }
        
        .loading {
            text-align: center;
            padding: 40px;
            color: #666;
        }
        
        .no-results {
            text-align: center;
            padding: 40px;
            color: #999;
        }
</style>
{% endblock %}

{% block content %}
<div class="filter-container">
            <div class="filter-panel">
                <h2>手机筛选器</h2>
                <form id="filterForm">
                    <div class="filter-row">
                        <div class="filter-group">
                            <label for="brand">品牌</label>
                            <select id="brand" name="brand">
                                <option value="">所有品牌</option>
                                {% for brand in brands %}
                                <option value="{{ brand }}">{{ brand }}</option>
                                {% endfor %}
                            </select>
                        </div>
                        
                        <div class="filter-group">
                            <label for="cpu">CPU型号</label>
                            <select id="cpu" name="cpu">
                                <option value="">所有CPU</option>
                                {% for cpu in cpu_models %}
                                <option value="{{ cpu }}">{{ cpu }}</option>
                                {% endfor %}
                            </select>
                        </div>
                        
                        <div class="filter-group">
                            <label for="memory">内存</label>
                            <select id="memory" name="memory">
                                <option value="">所有内存</option>
                                {% for memory in memory_sizes %}
                                <option value="{{ memory }}">{{ memory }}</option>
                                {% endfor %}
                            </select>
                        </div>
                    </div>
                    
                    <div class="filter-row">
                        <div class="filter-group">
                            <label for="color">颜色</label>
                            <select id="color" name="color">
                                <option value="">所有颜色</option>
                                {% for color in colors %}
                                <option value="{{ color }}">{{ color }}</option>
                                {% endfor %}
                            </select>
                        </div>
                        
                        <div class="filter-group">
                            <label for="os">操作系统</label>
                            <select id="os" name="os">
                                <option value="">所有系统</option>
                                {% for os in os_types %}
                                <option value="{{ os }}">{{ os }}</option>
                                {% endfor %}
                            </select>
                        </div>
                        
                        <div class="filter-group">
                            <label>价格范围</label>
                            <div class="price-range">
                                <input type="number" id="min_price" name="min_price" placeholder="最低价格" min="{{ min_price }}" max="{{ max_price }}">
                                <span>-</span>
                                <input type="number" id="max_price" name="max_price" placeholder="最高价格" min="{{ min_price }}" max="{{ max_price }}">
                            </div>
                        </div>
                    </div>
                    
                    <div class="filter-row">
                        <div class="filter-group">
                            <label>评分范围</label>
                            <div class="price-range">
                                <input type="number" id="rating_min" name="rating_min" placeholder="最低评分" min="0" max="5" step="0.1">
                                <span>-</span>
                                <input type="number" id="rating_max" name="rating_max" placeholder="最高评分" min="0" max="5" step="0.1">
                            </div>
                        </div>
                        
                        <div class="filter-group">
                            <label for="sort_by">排序方式</label>
                            <select id="sort_by" name="sort_by">
                                <option value="id">默认排序</option>
                                <option value="price">价格</option>
                                <option value="rating">评分</option>
                                <option value="brand">品牌</option>
                            </select>
                        </div>
                        
                        <div class="filter-group">
                            <label for="sort_order">排序顺序</label>
                            <select id="sort_order" name="sort_order">
                                <option value="asc">升序</option>
                                <option value="desc">降序</option>
                            </select>
                        </div>
                    </div>
                    
                    <div class="filter-actions">
                        <button type="button" class="btn btn-secondary" onclick="resetFilters()">重置</button>
                        <button type="submit" class="btn btn-primary">筛选</button>
                    </div>
                </form>
            </div>
            
            <div class="results-container" id="resultsContainer" style="display: none;">
                <div class="results-header">
                    <h3>筛选结果</h3>
                    <div class="results-stats" id="resultsStats"></div>
                </div>
                
                <div id="resultsContent"></div>
            </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
        let currentPage = 1;

        document.getElementById('filterForm').addEventListener('submit', function(e) {
            e.preventDefault();
            currentPage = 1;
            loadResults();
        });

        function resetFilters() {
            document.getElementById('filterForm').reset();
            document.getElementById('resultsContainer').style.display = 'none';
        }

        function loadResults(page = 1) {
            const formData = new FormData(document.getElementById('filterForm'));
            const params = new URLSearchParams();

            for (let [key, value] of formData.entries()) {
                if (value.trim() !== '') {
                    params.append(key, value);
                }
            }

            params.append('page', page);

            // 显示加载状态
            document.getElementById('resultsContainer').style.display = 'block';
            document.getElementById('resultsContent').innerHTML = '<div class="loading">正在加载...</div>';

            fetch(`/api/filter-phones/?${params.toString()}`, {
                headers: {
                    'X-Requested-With': 'XMLHttpRequest'
                }
            })
            .then(response => response.json())
            .then(data => {
                displayResults(data);
                currentPage = page;
            })
            .catch(error => {
                console.error('Error:', error);
                document.getElementById('resultsContent').innerHTML = '<div class="no-results">加载失败，请重试</div>';
            });
        }

        function displayResults(data) {
            console.log('收到的数据:', data);
            console.log('phones数组长度:', data.phones ? data.phones.length : 'undefined');

            const statsHtml = `
                <span>共找到 ${data.total_count} 条记录</span>
                <span>平均价格: ¥${data.avg_price.toFixed(2)}</span>
                <span>平均评分: ${data.avg_rating.toFixed(1)}</span>
            `;
            document.getElementById('resultsStats').innerHTML = statsHtml;

            if (!data.phones || data.phones.length === 0) {
                console.log('没有手机数据，显示空结果');
                document.getElementById('resultsContent').innerHTML = '<div class="no-results">没有找到符合条件的手机</div>';
                return;
            }

            let tableHtml = `
                <table class="phone-table">
                    <thead>
                        <tr>
                            <th onclick="sortBy('brand')">品牌</th>
                            <th onclick="sortBy('model')">型号</th>
                            <th onclick="sortBy('color')">颜色</th>
                            <th onclick="sortBy('price')">价格</th>
                            <th onclick="sortBy('cpu')">CPU</th>
                            <th onclick="sortBy('memory')">内存</th>
                            <th onclick="sortBy('battery')">电池</th>
                            <th onclick="sortBy('os')">系统</th>
                            <th onclick="sortBy('rating')">评分</th>
                        </tr>
                    </thead>
                    <tbody>
            `;

            data.phones.forEach(phone => {
                tableHtml += `
                    <tr>
                        <td>${phone.brand}</td>
                        <td>${phone.model}</td>
                        <td>${phone.color}</td>
                        <td>¥${phone.price.toFixed(2)}</td>
                        <td>${phone.cpu}</td>
                        <td>${phone.memory}</td>
                        <td>${phone.battery}</td>
                        <td>${phone.os}</td>
                        <td>${phone.rating.toFixed(1)}</td>
                    </tr>
                `;
            });

            tableHtml += '</tbody></table>';

            // 添加分页
            if (data.total_pages > 1) {
                tableHtml += '<div class="pagination">';

                if (data.has_previous) {
                    tableHtml += `<button onclick="loadResults(${data.current_page - 1})">上一页</button>`;
                }

                for (let i = Math.max(1, data.current_page - 2); i <= Math.min(data.total_pages, data.current_page + 2); i++) {
                    const activeClass = i === data.current_page ? 'active' : '';
                    tableHtml += `<button class="${activeClass}" onclick="loadResults(${i})">${i}</button>`;
                }

                if (data.has_next) {
                    tableHtml += `<button onclick="loadResults(${data.current_page + 1})">下一页</button>`;
                }

                tableHtml += '</div>';
            }

            document.getElementById('resultsContent').innerHTML = tableHtml;
        }

        function sortBy(field) {
            document.getElementById('sort_by').value = field;
            const currentOrder = document.getElementById('sort_order').value;
            document.getElementById('sort_order').value = currentOrder === 'asc' ? 'desc' : 'asc';
            loadResults(currentPage);
        }

        // 页面加载完成后自动加载所有数据
        document.addEventListener('DOMContentLoaded', function() {
            loadResults();
        });
</script>
{% endblock %}
