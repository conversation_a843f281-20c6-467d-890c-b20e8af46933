<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>手机数据分析系统 - 登录</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .login-container {
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
            width: 900px;
            max-width: 90vw;
            display: flex;
            min-height: 600px;
        }

        .login-left {
            flex: 1;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            color: white;
            padding: 40px;
            text-align: center;
        }

        .login-left h1 {
            font-size: 2.5em;
            margin-bottom: 20px;
            font-weight: 300;
        }

        .login-left p {
            font-size: 1.1em;
            opacity: 0.9;
            line-height: 1.6;
        }

        .login-right {
            flex: 1;
            padding: 60px 40px;
            display: flex;
            flex-direction: column;
            justify-content: center;
        }

        .login-form h2 {
            color: #333;
            margin-bottom: 30px;
            font-size: 2em;
            font-weight: 300;
        }

        .role-selector {
            margin-bottom: 30px;
        }

        .role-selector h3 {
            color: #666;
            margin-bottom: 15px;
            font-size: 1.1em;
            font-weight: 500;
        }

        .role-options {
            display: flex;
            gap: 15px;
            flex-wrap: wrap;
        }

        .role-option {
            flex: 1;
            min-width: 120px;
        }

        .role-option input[type="radio"] {
            display: none;
        }

        .role-option label {
            display: block;
            padding: 15px 10px;
            border: 2px solid #e0e0e0;
            border-radius: 10px;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s;
            background: #f9f9f9;
        }

        .role-option input[type="radio"]:checked + label {
            border-color: #667eea;
            background: #667eea;
            color: white;
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.3);
        }

        .role-title {
            font-weight: bold;
            font-size: 1em;
            margin-bottom: 5px;
        }

        .role-desc {
            font-size: 0.8em;
            opacity: 0.8;
        }

        .form-group {
            margin-bottom: 25px;
        }

        .form-group label {
            display: block;
            margin-bottom: 8px;
            color: #555;
            font-weight: 500;
        }

        .form-group input {
            width: 100%;
            padding: 15px;
            border: 2px solid #e0e0e0;
            border-radius: 10px;
            font-size: 1em;
            transition: border-color 0.3s;
        }

        .form-group input:focus {
            outline: none;
            border-color: #667eea;
        }

        .login-btn {
            width: 100%;
            padding: 15px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            border-radius: 10px;
            font-size: 1.1em;
            font-weight: 500;
            cursor: pointer;
            transition: transform 0.3s;
        }

        .login-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(102, 126, 234, 0.3);
        }

        .messages {
            margin-bottom: 20px;
        }

        .alert {
            padding: 12px 15px;
            border-radius: 8px;
            margin-bottom: 10px;
        }

        .alert-success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .alert-error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }

        .alert-warning {
            background: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }

        @media (max-width: 768px) {
            .login-container {
                flex-direction: column;
                width: 95vw;
                min-height: auto;
            }

            .login-left {
                padding: 30px 20px;
            }

            .login-left h1 {
                font-size: 2em;
            }

            .login-right {
                padding: 40px 30px;
            }

            .role-options {
                flex-direction: column;
            }

            .role-option {
                min-width: auto;
            }
        }
    </style>
</head>
<body>
    <div class="login-container">
        <div class="login-left">
            <h1>📱 手机数据分析系统</h1>
            <p>专业的手机市场数据分析平台<br>为您提供全面的市场洞察和决策支持</p>
        </div>
        
        <div class="login-right">
            <form method="post" class="login-form">
                {% csrf_token %}
                
                <h2>用户登录</h2>
                
                <!-- 消息提示 -->
                {% if messages %}
                    <div class="messages">
                        {% for message in messages %}
                            <div class="alert alert-{{ message.tags }}">
                                {{ message }}
                            </div>
                        {% endfor %}
                    </div>
                {% endif %}
                
                <!-- 角色选择 -->
                <div class="role-selector">
                    <h3>选择登录角色</h3>
                    <div class="role-options">
                        {% for role in roles %}
                        <div class="role-option">
                            <input type="radio" id="role_{{ role.name }}" name="role_type" value="{{ role.name }}"
                                   {% if role.name == 'user' %}checked{% endif %}>
                            <label for="role_{{ role.name }}">
                                <div class="role-title">
                                    {% if role.name == 'admin' %}🔧{% elif role.name == 'analyst' %}📊{% else %}👤{% endif %}
                                    {{ role.display_name }}
                                </div>
                                <div class="role-desc">
                                    {% if role.name == 'admin' %}数据管理
                                    {% elif role.name == 'analyst' %}高级分析
                                    {% else %}基础查看{% endif %}
                                </div>
                            </label>
                        </div>
                        {% endfor %}
                    </div>
                </div>
                
                <!-- 用户名 -->
                <div class="form-group">
                    <label for="username">用户名</label>
                    <input type="text" id="username" name="username" required>
                </div>
                
                <!-- 密码 -->
                <div class="form-group">
                    <label for="password">密码</label>
                    <input type="password" id="password" name="password" required>
                </div>
                
                <!-- 登录按钮 -->
                <button type="submit" class="login-btn">登录系统</button>
            </form>
        </div>
    </div>
</body>
</html>
