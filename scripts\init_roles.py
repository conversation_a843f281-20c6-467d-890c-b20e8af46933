#!/usr/bin/env python
"""
初始化角色和权限数据的脚本
"""
import os
import sys
import django

# 添加项目路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# 设置Django环境
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'phone_analysis.settings')
django.setup()

from analysis.models import Role, CustomUser
from analysis.decorators import ROLE_PERMISSIONS

def create_roles():
    """创建默认角色"""
    roles_data = [
        {
            'name': 'admin',
            'display_name': '管理员',
            'description': '系统管理员，拥有所有权限，可以管理用户、角色和系统配置',
            'permissions': ROLE_PERMISSIONS['admin']
        },
        {
            'name': 'analyst',
            'display_name': '数据分析师',
            'description': '数据分析师，拥有高级分析权限，可以进行预测分析和数据对比',
            'permissions': ROLE_PERMISSIONS['analyst']
        },
        {
            'name': 'user',
            'display_name': '普通用户',
            'description': '普通用户，只能查看基础分析功能',
            'permissions': ROLE_PERMISSIONS['user']
        }
    ]
    
    for role_data in roles_data:
        role, created = Role.objects.get_or_create(
            name=role_data['name'],
            defaults={
                'display_name': role_data['display_name'],
                'description': role_data['description'],
                'permissions': role_data['permissions']
            }
        )
        
        if created:
            print(f"✅ 创建角色: {role.display_name}")
        else:
            # 更新权限配置
            role.permissions = role_data['permissions']
            role.save()
            print(f"🔄 更新角色: {role.display_name}")

def create_default_users():
    """创建默认用户"""
    users_data = [
        {
            'username': 'admin',
            'password': 'admin123',
            'email': '<EMAIL>',
            'first_name': '系统',
            'last_name': '管理员',
            'role_name': 'admin',
            'is_superuser': True,
            'is_staff': True
        },
        {
            'username': 'analyst',
            'password': 'analyst123',
            'email': '<EMAIL>',
            'first_name': '数据',
            'last_name': '分析师',
            'role_name': 'analyst'
        },
        {
            'username': 'user',
            'password': 'user123',
            'email': '<EMAIL>',
            'first_name': '普通',
            'last_name': '用户',
            'role_name': 'user'
        }
    ]
    
    for user_data in users_data:
        if not CustomUser.objects.filter(username=user_data['username']).exists():
            role = Role.objects.get(name=user_data['role_name'])
            
            user = CustomUser.objects.create_user(
                username=user_data['username'],
                password=user_data['password'],
                email=user_data['email'],
                first_name=user_data['first_name'],
                last_name=user_data['last_name'],
                role=role,
                is_superuser=user_data.get('is_superuser', False),
                is_staff=user_data.get('is_staff', False)
            )
            
            print(f"✅ 创建用户: {user.username} ({role.display_name})")
        else:
            print(f"⚠️  用户已存在: {user_data['username']}")

def main():
    """主函数"""
    print("🚀 开始初始化角色和权限系统...")
    
    try:
        # 创建角色
        print("\n📋 创建角色...")
        create_roles()
        
        # 创建默认用户
        print("\n👥 创建默认用户...")
        create_default_users()
        
        print("\n✅ 角色和权限系统初始化完成！")
        print("\n📝 默认用户账号:")
        print("   管理员: admin / admin123")
        print("   分析师: analyst / analyst123")
        print("   普通用户: user / user123")
        
    except Exception as e:
        print(f"\n❌ 初始化失败: {str(e)}")
        sys.exit(1)

if __name__ == '__main__':
    main()
