"""phone_analysis URL Configuration

The `urlpatterns` list routes URLs to views. For more information please see:
    https://docs.djangoproject.com/en/3.2/topics/http/urls/
Examples:
Function views
    1. Add an import:  from my_app import views
    2. Add a URL to urlpatterns:  path('', views.home, name='home')
Class-based views
    1. Add an import:  from other_app.views import Home
    2. Add a URL to urlpatterns:  path('', Home.as_view(), name='home')
Including another URLconf
    1. Import the include() function: from django.urls import include, path
    2. Add a URL to urlpatterns:  path('blog/', include('blog.urls'))
"""
from django.contrib import admin
from django.urls import path
from analysis import views, auth_views

urlpatterns = [
    path('admin/', admin.site.urls),

    # 认证相关
    path('login/', auth_views.login_view, name='login'),
    path('logout/', auth_views.logout_view, name='logout'),

    # 用户管理（管理员权限）
    path('users/', auth_views.user_management, name='user_management'),
    path('users/create/', auth_views.create_user, name='create_user'),
    path('users/<int:user_id>/update/', auth_views.update_user, name='update_user'),
    path('users/<int:user_id>/delete/', auth_views.delete_user, name='delete_user'),
    path('logs/', auth_views.login_logs, name='login_logs'),

    # 分析功能
    path('', views.index, name='index'),
    path('api/brand-stats/', views.get_brand_stats, name='brand_stats'),
    path('api/price-distribution/', views.get_price_distribution, name='price_distribution'),
    path('brand-analysis/', views.brand_analysis, name='brand_analysis'),
    path('price-distribution/', views.price_distribution, name='price_distribution'),
    path('cpu-analysis/', views.cpu_analysis, name='cpu_analysis'),
    path('api/cpu-stats/', views.get_cpu_stats, name='cpu_stats'),
    path('memory-analysis/', views.memory_analysis, name='memory_analysis'),
    path('api/memory-stats/', views.get_memory_stats, name='memory_stats'),
    path('phone-filter/', views.phone_filter, name='phone_filter'),
    path('api/filter-phones/', views.filter_phones, name='filter_phones'),
    path('prediction-analysis/', views.prediction_analysis, name='prediction_analysis'),
    path('api/price-prediction/', views.get_price_prediction, name='price_prediction'),
    path('api/brand-trend-prediction/', views.get_brand_trend_prediction, name='brand_trend_prediction'),
    path('price-comparison/', views.price_comparison, name='price_comparison'),
    path('api/price-range-analysis/', views.get_price_range_analysis, name='price_range_analysis'),
    path('api/similar-phones/', views.get_similar_phones, name='similar_phones'),
]
