from django.db import models
from django.contrib.auth.models import AbstractUser

class Role(models.Model):
    """用户角色模型"""
    ROLE_CHOICES = [
        ('admin', '管理员'),
        ('analyst', '数据分析师'),
        ('user', '普通用户'),
    ]

    name = models.CharField(max_length=20, choices=ROLE_CHOICES, unique=True, verbose_name='角色名称')
    display_name = models.CharField(max_length=50, verbose_name='显示名称')
    description = models.TextField(blank=True, verbose_name='角色描述')
    permissions = models.JSONField(default=dict, verbose_name='权限配置')
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='创建时间')

    class Meta:
        db_table = 'user_roles'
        verbose_name = '用户角色'
        verbose_name_plural = '用户角色'

    def __str__(self):
        return self.display_name

class CustomUser(AbstractUser):
    """扩展用户模型"""
    role = models.ForeignKey(Role, on_delete=models.SET_NULL, null=True, blank=True, verbose_name='用户角色')
    phone = models.CharField(max_length=20, blank=True, verbose_name='手机号')
    department = models.CharField(max_length=100, blank=True, verbose_name='部门')
    last_login_ip = models.GenericIPAddressField(null=True, blank=True, verbose_name='最后登录IP')
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='创建时间')
    updated_at = models.DateTimeField(auto_now=True, verbose_name='更新时间')

    class Meta:
        db_table = 'users'
        verbose_name = '用户'
        verbose_name_plural = '用户'

    def has_permission(self, permission_key):
        """检查用户是否有特定权限"""
        if not self.role:
            # 如果没有角色，给基础权限
            from .decorators import Permissions
            basic_permissions = [Permissions.VIEW_DASHBOARD, Permissions.VIEW_BASIC_ANALYSIS]
            return permission_key in basic_permissions
        return self.role.permissions.get(permission_key, False)

    def get_role_display(self):
        """获取角色显示名称"""
        return self.role.display_name if self.role else '未分配角色'

class Phone(models.Model):
    brand = models.CharField(max_length=50)
    model = models.CharField(max_length=200)
    color = models.CharField(max_length=200)
    price = models.DecimalField(max_digits=10, decimal_places=2, null=True)
    cpu = models.CharField(max_length=100)
    memory = models.CharField(max_length=100)
    battery = models.CharField(max_length=50)
    os = models.CharField(max_length=100)
    camera = models.TextField()
    rating = models.DecimalField(max_digits=3, decimal_places=1, null=True)

    class Meta:
        db_table = 'phones'

class UserLoginLog(models.Model):
    """用户登录日志"""
    user = models.ForeignKey(CustomUser, on_delete=models.CASCADE, verbose_name='用户')
    login_time = models.DateTimeField(auto_now_add=True, verbose_name='登录时间')
    ip_address = models.GenericIPAddressField(verbose_name='IP地址')
    user_agent = models.TextField(verbose_name='用户代理')
    login_result = models.BooleanField(default=True, verbose_name='登录结果')

    class Meta:
        db_table = 'user_login_logs'
        verbose_name = '登录日志'
        verbose_name_plural = '登录日志'
        ordering = ['-login_time']
