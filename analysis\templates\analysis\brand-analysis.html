{% extends "base.html" %}

{% block title %}品牌分析 - 手机数据分析平台{% endblock %}

{% block content %}
<h1>品牌分析</h1>
<div id="brandChart" class="chart"></div>
{% endblock %}

{% block extra_js %}
<script>
    var chartDom = document.getElementById('brandChart');
    var myChart = echarts.init(chartDom);

    // 从后端获取数据并渲染图表
    $.get('/api/brand-stats/', function(data) {
        var option = {
            title: {
                text: '手机品牌分析',
                left: 'center'
            },
            tooltip: {
                trigger: 'axis',
                axisPointer: {
                    type: 'cross'
                }
            },
            legend: {
                data: ['销量', '平均价格'],
                top: 30
            },
            xAxis: {
                data: data.brands,
                axisLabel: {
                    interval: 0,
                    rotate: 30
                }
            },
            yAxis: [
                {
                    type: 'value',
                    name: '销量',
                    position: 'left'
                },
                {
                    type: 'value',
                    name: '平均价格(元)',
                    position: 'right'
                }
            ],
            series: [
                {
                    name: '销量',
                    type: 'bar',
                    data: data.counts,
                    itemStyle: {
                        color: '#5470c6'
                    }
                },
                {
                    name: '平均价格',
                    type: 'line',
                    yAxisIndex: 1,
                    data: data.avg_prices,
                    itemStyle: {
                        color: '#91cc75'
                    }
                }
            ]
        };
        myChart.setOption(option);
    });

    // 监听窗口大小变化，调整图表大小
    window.addEventListener('resize', function() {
        myChart.resize();
    });
</script>
{% endblock %}