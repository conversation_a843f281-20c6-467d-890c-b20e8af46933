from functools import wraps
from django.http import JsonResponse
from django.shortcuts import redirect
from django.contrib import messages
from django.urls import reverse

def get_client_ip(request):
    """获取客户端IP地址"""
    x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
    if x_forwarded_for:
        ip = x_forwarded_for.split(',')[0]
    else:
        ip = request.META.get('REMOTE_ADDR')
    return ip

def check_permission_by_username(username, permission_key):
    """基于用户名检查权限的临时函数"""
    print(f"DEBUG: 检查权限 - 用户: {username}, 权限: {permission_key}")

    # 管理员权限
    if username == 'admin':
        print(f"DEBUG: 管理员用户，允许所有权限")
        print(f"DEBUG: 查找的权限: {permission_key}")
        return True

    # 分析师权限
    elif username == 'analyst':
        analyst_permissions = [
            Permissions.VIEW_DASHBOARD,
            Permissions.VIEW_BASIC_ANALYSIS,
            Permissions.VIEW_ADVANCED_ANALYSIS,
            Permissions.VIEW_PREDICTION,
            Permissions.VIEW_COMPARISON,
            Permissions.EXPORT_DATA
        ]
        result = permission_key in analyst_permissions
        print(f"DEBUG: 分析师用户，权限检查结果: {result}")
        print(f"DEBUG: 权限列表: {analyst_permissions}")
        print(f"DEBUG: 查找的权限: {permission_key}")
        return result

    # 普通用户权限
    else:
        user_permissions = [
            Permissions.VIEW_DASHBOARD,
            Permissions.VIEW_BASIC_ANALYSIS
        ]
        result = permission_key in user_permissions
        print(f"DEBUG: 普通用户，权限检查结果: {result}")
        return result

def check_login(view_func):
    """检查用户是否登录"""
    @wraps(view_func)
    def wrapper(request, *args, **kwargs):
        if not request.user.is_authenticated:
            if request.headers.get('Content-Type') == 'application/json' or request.path.startswith('/api/'):
                return JsonResponse({
                    'success': False,
                    'error': '请先登录',
                    'redirect': '/login/'
                }, status=401)
            else:
                messages.warning(request, '请先登录')
                return redirect('login')
        return view_func(request, *args, **kwargs)
    return wrapper

def require_permission(permission_key):
    """检查用户权限装饰器"""
    def decorator(view_func):
        @wraps(view_func)
        def wrapper(request, *args, **kwargs):
            # 首先检查是否登录
            if not request.user.is_authenticated:
                if request.headers.get('Content-Type') == 'application/json' or request.path.startswith('/api/'):
                    return JsonResponse({
                        'success': False,
                        'error': '请先登录',
                        'redirect': '/login/'
                    }, status=401)
                else:
                    messages.warning(request, '请先登录')
                    return redirect('login')
            
            # 检查权限 - 基于用户名的临时权限系统
            try:
                if hasattr(request.user, 'has_permission'):
                    has_perm = request.user.has_permission(permission_key)
                else:
                    # 基于用户名的权限检查
                    has_perm = check_permission_by_username(request.user.username, permission_key)
            except:
                # 如果权限检查出错，基于用户名给权限
                has_perm = check_permission_by_username(request.user.username, permission_key)

            if not has_perm:
                if request.headers.get('Content-Type') == 'application/json' or request.path.startswith('/api/'):
                    return JsonResponse({
                        'success': False,
                        'error': f'权限不足，需要{permission_key}权限',
                        'permission_required': permission_key
                    }, status=403)
                else:
                    messages.error(request, f'权限不足，需要{permission_key}权限')
                    return redirect('index')
            
            return view_func(request, *args, **kwargs)
        return wrapper
    return decorator

def require_role(role_name):
    """检查用户角色装饰器"""
    def decorator(view_func):
        @wraps(view_func)
        def wrapper(request, *args, **kwargs):
            # 首先检查是否登录
            if not request.user.is_authenticated:
                if request.headers.get('Content-Type') == 'application/json' or request.path.startswith('/api/'):
                    return JsonResponse({
                        'success': False,
                        'error': '请先登录',
                        'redirect': '/login/'
                    }, status=401)
                else:
                    messages.warning(request, '请先登录')
                    return redirect('login')
            
            # 检查角色
            if not request.user.role or request.user.role.name != role_name:
                if request.headers.get('Content-Type') == 'application/json' or request.path.startswith('/api/'):
                    return JsonResponse({
                        'success': False,
                        'error': f'权限不足，需要{role_name}角色',
                        'role_required': role_name
                    }, status=403)
                else:
                    messages.error(request, f'权限不足，需要{role_name}角色')
                    return redirect('index')
            
            return view_func(request, *args, **kwargs)
        return wrapper
    return decorator

# 预定义的权限常量
class Permissions:
    # 基础权限
    VIEW_DASHBOARD = 'view_dashboard'
    VIEW_BASIC_ANALYSIS = 'view_basic_analysis'
    
    # 数据分析权限
    VIEW_ADVANCED_ANALYSIS = 'view_advanced_analysis'
    VIEW_PREDICTION = 'view_prediction'
    VIEW_COMPARISON = 'view_comparison'
    EXPORT_DATA = 'export_data'
    
    # 管理员权限
    MANAGE_USERS = 'manage_users'
    MANAGE_ROLES = 'manage_roles'
    MANAGE_DATA = 'manage_data'
    VIEW_LOGS = 'view_logs'
    SYSTEM_CONFIG = 'system_config'

# 角色权限配置
ROLE_PERMISSIONS = {
    'admin': {
        # 管理员拥有所有权限
        Permissions.VIEW_DASHBOARD: True,
        Permissions.VIEW_BASIC_ANALYSIS: True,
        Permissions.VIEW_ADVANCED_ANALYSIS: True,
        Permissions.VIEW_PREDICTION: True,
        Permissions.VIEW_COMPARISON: True,
        Permissions.EXPORT_DATA: True,
        Permissions.MANAGE_USERS: True,
        Permissions.MANAGE_ROLES: True,
        Permissions.MANAGE_DATA: True,
        Permissions.VIEW_LOGS: True,
        Permissions.SYSTEM_CONFIG: True,
    },
    'analyst': {
        # 数据分析师拥有高级分析权限
        Permissions.VIEW_DASHBOARD: True,
        Permissions.VIEW_BASIC_ANALYSIS: True,
        Permissions.VIEW_ADVANCED_ANALYSIS: True,
        Permissions.VIEW_PREDICTION: True,
        Permissions.VIEW_COMPARISON: True,
        Permissions.EXPORT_DATA: True,
        Permissions.MANAGE_USERS: False,
        Permissions.MANAGE_ROLES: False,
        Permissions.MANAGE_DATA: False,
        Permissions.VIEW_LOGS: False,
        Permissions.SYSTEM_CONFIG: False,
    },
    'user': {
        # 普通用户只有基础查看权限
        Permissions.VIEW_DASHBOARD: True,
        Permissions.VIEW_BASIC_ANALYSIS: True,
        Permissions.VIEW_ADVANCED_ANALYSIS: False,
        Permissions.VIEW_PREDICTION: False,
        Permissions.VIEW_COMPARISON: False,
        Permissions.EXPORT_DATA: False,
        Permissions.MANAGE_USERS: False,
        Permissions.MANAGE_ROLES: False,
        Permissions.MANAGE_DATA: False,
        Permissions.VIEW_LOGS: False,
        Permissions.SYSTEM_CONFIG: False,
    }
}
