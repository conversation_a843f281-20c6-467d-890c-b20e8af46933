import pymysql

try:
    conn = pymysql.connect(host='localhost', user='root', password='root', database='phone_db')
    cursor = conn.cursor()
    
    # 删除可能存在的表
    tables = ['user_login_logs', 'users_user_permissions', 'users_groups', 'users', 'user_roles']
    for table in tables:
        try:
            cursor.execute(f'DROP TABLE IF EXISTS {table}')
            print(f'删除表 {table}')
        except Exception as e:
            print(f'删除表 {table} 失败: {e}')
    
    # 清理迁移记录
    cursor.execute('DELETE FROM django_migrations WHERE app = "analysis"')
    cursor.execute('DELETE FROM django_migrations WHERE app = "admin"')
    
    conn.commit()
    conn.close()
    print('数据库清理完成')
    
except Exception as e:
    print(f'清理失败: {e}')
