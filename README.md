# 手机数据分析项目

这是一个基于Django的手机数据分析项目，提供数据可视化、预测分析和用户权限管理功能。

## 功能特性

- 📊 **数据可视化**: 品牌分析、价格分布、CPU分析、内存分析
- 🔍 **智能筛选**: 多条件手机筛选和搜索
- 🤖 **预测分析**: 价格趋势预测、品牌趋势预测
- 📈 **对比分析**: 同价位手机对比、相似手机推荐
- 👥 **用户管理**: 基于角色的权限控制系统
- 📱 **响应式设计**: 支持多设备访问

## 快速开始

### 1. 环境准备

确保你的系统已安装：
- Python 3.8+
- MySQL 5.7+ 或 MariaDB 10.3+

### 2. 安装依赖

```bash
pip install -r requirements.txt
```

### 3. 数据库配置

1. 创建MySQL数据库：
```sql
CREATE DATABASE phone_db CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
```

2. 修改数据库配置（如需要）：
编辑 `phone_analysis/settings.py` 中的数据库配置：
```python
DATABASES = {
    'default': {
        'ENGINE': 'django.db.backends.mysql',
        'NAME': 'phone_db',
        'USER': 'root',        # 修改为你的用户名
        'PASSWORD': 'root',    # 修改为你的密码
        'HOST': 'localhost',
        'PORT': '3306',
    }
}
```

### 4. 初始化数据库

运行数据库迁移：
```bash
python manage.py makemigrations
python manage.py migrate
```

### 5. 创建初始用户和角色

```bash
python scripts/setup_database.py
```

这将创建以下默认用户：
- **管理员**: `admin` / `admin123`
- **分析师**: `analyst` / `analyst123`  
- **普通用户**: `user` / `user123`

### 6. 导入手机数据

如果有 `phone.csv` 数据文件：
```bash
python scripts/import_data.py
```

### 7. 启动服务器

```bash
python manage.py runserver
```

访问 http://localhost:8000/login/ 进行登录

## 项目结构

```
phone_analysis/
├── analysis/                 # 主应用
│   ├── models.py            # 数据模型
│   ├── views.py             # 视图逻辑
│   ├── templates/           # HTML模板
│   └── migrations/          # 数据库迁移
├── phone_analysis/          # 项目配置
│   ├── settings.py          # 设置文件
│   └── urls.py              # URL配置
├── scripts/                 # 工具脚本
│   ├── setup_database.py    # 数据库初始化
│   └── import_data.py       # 数据导入
├── static/                  # 静态文件
├── phone.csv               # 手机数据文件
└── manage.py               # Django管理脚本
```

## 用户角色权限

### 管理员 (admin)
- ✅ 所有功能权限
- ✅ 用户管理
- ✅ 系统配置

### 数据分析师 (analyst)  
- ✅ 数据分析和可视化
- ✅ 预测分析
- ✅ 数据导出
- ❌ 用户管理

### 普通用户 (user)
- ✅ 基础数据查看
- ✅ 简单筛选
- ❌ 高级分析功能

## 主要功能模块

### 1. 数据分析
- 品牌统计分析
- 价格分布分析  
- CPU性能分析
- 内存配置分析

### 2. 智能筛选
- 多维度筛选条件
- 实时搜索结果
- 分页显示

### 3. 预测分析
- 基于机器学习的价格预测
- 品牌趋势预测
- 市场走势分析

### 4. 对比功能
- 同价位手机对比
- K-Means聚类分析
- 相似产品推荐

## 故障排除

### 常见问题

1. **数据库连接失败**
   - 检查MySQL服务是否启动
   - 验证数据库配置信息
   - 确认数据库已创建

2. **依赖安装失败**
   - 使用虚拟环境：`python -m venv venv`
   - 激活环境后再安装依赖

3. **数据导入失败**
   - 确认 `phone.csv` 文件存在
   - 检查文件编码格式（建议UTF-8）

4. **权限错误**
   - 重新运行 `python scripts/setup_database.py`
   - 检查用户角色配置

## 开发说明

### 添加新功能
1. 在 `analysis/views.py` 中添加视图函数
2. 在 `analysis/templates/` 中创建模板
3. 在 `phone_analysis/urls.py` 中配置URL

### 数据模型修改
1. 修改 `analysis/models.py`
2. 运行 `python manage.py makemigrations`
3. 运行 `python manage.py migrate`

## 技术栈

- **后端**: Django 3.2, Python 3.8+
- **数据库**: MySQL 5.7+
- **数据分析**: Pandas, NumPy, Scikit-learn
- **前端**: HTML5, CSS3, JavaScript, Bootstrap
- **图表**: Chart.js, ECharts

## 许可证

MIT License

## 联系方式

如有问题请提交 Issue 或联系开发团队。
