{% extends "base.html" %}

{% block title %}CPU 分析 - 手机数据分析平台{% endblock %}

{% block content %}
<h1>CPU 分析</h1>
<div id="cpuChart" class="chart"></div>
{% endblock %}

{% block extra_js %}
<script>
        var chartDom = document.getElementById('cpuChart');
        var myChart = echarts.init(chartDom);
        
        $.get('/api/cpu-stats/', function(data) {
            var option = {
                title: {
                    text: 'CPU型号分布分析',
                    left: 'center'
                },
                tooltip: {
                    trigger: 'axis',
                    axisPointer: {
                        type: 'cross'
                    }
                },
                legend: {
                    data: ['使用数量', '平均价格'],
                    top: 30
                },
                grid: {
                    left: '3%',
                    right: '4%',
                    bottom: '3%',
                    containLabel: true
                },
                xAxis: {
                    type: 'category',
                    data: data.cpu_models,
                    axisLabel: {
                        interval: 0,
                        rotate: 45
                    }
                },
                yAxis: [
                    {
                        type: 'value',
                        name: '使用数量',
                        position: 'left'
                    },
                    {
                        type: 'value',
                        name: '平均价格(元)',
                        position: 'right'
                    }
                ],
                series: [
                    {
                        name: '使用数量',
                        type: 'bar',
                        data: data.counts,
                        itemStyle: {
                            color: '#5470c6'
                        }
                    },
                    {
                        name: '平均价格',
                        type: 'line',
                        yAxisIndex: 1,
                        data: data.avg_prices,
                        itemStyle: {
                            color: '#91cc75'
                        }
                    }
                ]
            };
            myChart.setOption(option);
        });

        window.addEventListener('resize', function() {
            myChart.resize();
        });
</script>
{% endblock %}