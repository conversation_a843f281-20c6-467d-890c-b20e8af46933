from django.shortcuts import render, redirect, get_object_or_404
from django.contrib.auth import authenticate, login, logout
from django.contrib import messages
from django.http import JsonResponse
from django.views.decorators.csrf import csrf_exempt
from django.contrib.auth.decorators import login_required
from django.core.paginator import Paginator
from django.db.models import Q
import json
from .models import CustomUser, Role, UserLoginLog
from .decorators import require_permission, require_role, get_client_ip, Permissions

def login_view(request):
    """登录视图"""
    if request.method == 'POST':
        username = request.POST.get('username')
        password = request.POST.get('password')
        role_type = request.POST.get('role_type', 'user')  # 默认普通用户
        
        # 验证用户
        user = authenticate(request, username=username, password=password)
        
        if user is not None:
            # 暂时不检查角色匹配，直接登录
            login(request, user)

            # 记录登录日志（如果表存在的话）
            try:
                UserLoginLog.objects.create(
                    user=user,
                    ip_address=get_client_ip(request),
                    user_agent=request.META.get('HTTP_USER_AGENT', ''),
                    login_result=True
                )
            except:
                pass  # 如果表不存在就跳过

            # 更新最后登录IP（如果字段存在的话）
            try:
                user.last_login_ip = get_client_ip(request)
                user.save()
            except:
                pass  # 如果字段不存在就跳过

            # 根据用户名确定角色显示
            if user.username == 'admin':
                role_display = '管理员'
            elif user.username == 'analyst':
                role_display = '数据分析师'
            else:
                role_display = '普通用户'

            messages.success(request, f'欢迎回来，{role_display}！')
            return redirect('index')
        else:
            messages.error(request, '用户名或密码错误')
    
    # 获取所有可用角色
    roles = Role.objects.all()
    return render(request, 'auth/login.html', {'roles': roles})

def logout_view(request):
    """登出视图"""
    logout(request)
    messages.success(request, '已成功退出登录')
    return redirect('login')

# dashboard视图已移除，直接使用index页面

@require_permission(Permissions.MANAGE_USERS)
def user_management(request):
    """用户管理页面"""
    search = request.GET.get('search', '')
    role_filter = request.GET.get('role', '')
    
    users = CustomUser.objects.all()
    
    if search:
        users = users.filter(
            Q(username__icontains=search) |
            Q(first_name__icontains=search) |
            Q(last_name__icontains=search) |
            Q(email__icontains=search)
        )
    
    if role_filter:
        users = users.filter(role__name=role_filter)
    
    paginator = Paginator(users, 20)
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)
    
    roles = Role.objects.all()
    
    context = {
        'page_obj': page_obj,
        'roles': roles,
        'search': search,
        'role_filter': role_filter,
    }
    return render(request, 'auth/user_management.html', context)

@require_permission(Permissions.MANAGE_USERS)
@csrf_exempt
def create_user(request):
    """创建用户"""
    if request.method == 'POST':
        try:
            data = json.loads(request.body)
            
            # 检查用户名是否已存在
            if CustomUser.objects.filter(username=data['username']).exists():
                return JsonResponse({
                    'success': False,
                    'error': '用户名已存在'
                })
            
            # 创建用户
            user = CustomUser.objects.create_user(
                username=data['username'],
                email=data.get('email', ''),
                password=data['password'],
                first_name=data.get('first_name', ''),
                last_name=data.get('last_name', ''),
                phone=data.get('phone', ''),
                department=data.get('department', '')
            )
            
            # 分配角色
            if data.get('role_id'):
                role = Role.objects.get(id=data['role_id'])
                user.role = role
                user.save()
            
            return JsonResponse({
                'success': True,
                'message': '用户创建成功'
            })
            
        except Exception as e:
            return JsonResponse({
                'success': False,
                'error': str(e)
            })
    
    return JsonResponse({'success': False, 'error': '无效的请求方法'})

@require_permission(Permissions.MANAGE_USERS)
@csrf_exempt
def update_user(request, user_id):
    """更新用户信息"""
    if request.method == 'POST':
        try:
            user = get_object_or_404(CustomUser, id=user_id)
            data = json.loads(request.body)
            
            # 更新用户信息
            user.first_name = data.get('first_name', user.first_name)
            user.last_name = data.get('last_name', user.last_name)
            user.email = data.get('email', user.email)
            user.phone = data.get('phone', user.phone)
            user.department = data.get('department', user.department)
            user.is_active = data.get('is_active', user.is_active)
            
            # 更新角色
            if data.get('role_id'):
                role = Role.objects.get(id=data['role_id'])
                user.role = role
            
            # 更新密码（如果提供）
            if data.get('password'):
                user.set_password(data['password'])
            
            user.save()
            
            return JsonResponse({
                'success': True,
                'message': '用户信息更新成功'
            })
            
        except Exception as e:
            return JsonResponse({
                'success': False,
                'error': str(e)
            })
    
    return JsonResponse({'success': False, 'error': '无效的请求方法'})

@require_permission(Permissions.MANAGE_USERS)
@csrf_exempt
def delete_user(request, user_id):
    """删除用户"""
    if request.method == 'POST':
        try:
            user = get_object_or_404(CustomUser, id=user_id)
            
            # 不能删除自己
            if user.id == request.user.id:
                return JsonResponse({
                    'success': False,
                    'error': '不能删除自己的账户'
                })
            
            user.delete()
            
            return JsonResponse({
                'success': True,
                'message': '用户删除成功'
            })
            
        except Exception as e:
            return JsonResponse({
                'success': False,
                'error': str(e)
            })
    
    return JsonResponse({'success': False, 'error': '无效的请求方法'})

@require_permission(Permissions.VIEW_LOGS)
def login_logs(request):
    """登录日志查看"""
    search = request.GET.get('search', '')
    
    logs = UserLoginLog.objects.select_related('user').all()
    
    if search:
        logs = logs.filter(
            Q(user__username__icontains=search) |
            Q(ip_address__icontains=search)
        )
    
    paginator = Paginator(logs, 50)
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)
    
    context = {
        'page_obj': page_obj,
        'search': search,
    }
    return render(request, 'auth/login_logs.html', context)
