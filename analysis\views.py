from django.shortcuts import render, redirect
from django.http import JsonResponse
from .models import Phone
from django.db.models import Avg, Count, Q, Min, Max
from django.core.paginator import Paginator
import pandas as pd
import numpy as np
from sklearn.linear_model import LinearRegression
from sklearn.preprocessing import LabelEncoder, StandardScaler
from sklearn.model_selection import train_test_split
from sklearn.metrics import mean_squared_error, r2_score
from sklearn.cluster import KMeans
import json
import re
from datetime import datetime, timedelta
from .decorators import check_login, require_permission, Permissions

def login_view(request):
    if request.method == 'POST':
        username = request.POST.get('username')
        password = request.POST.get('password')
        
        if username == '123' and password == '123':
            request.session['is_logged_in'] = True
            return redirect('index')
        else:
            return render(request, 'analysis/login.html', {'error': '用户名或密码错误'})
    
    return render(request, 'analysis/login.html')

def check_login(view_func):
    def wrapper(request, *args, **kwargs):
        if not request.session.get('is_logged_in'):
            return redirect('login')
        return view_func(request, *args, **kwargs)
    return wrapper

def index(request):
    """主页面 - 显示用户权限相关的功能"""
    user = request.user

    # 基于用户名确定角色，所有用户都有所有权限
    if user.username == 'admin':
        role_display = '管理员'
    elif user.username == 'analyst':
        role_display = '数据分析师'
    else:
        role_display = '普通用户'

    # 所有用户都有所有权限
    can_view_prediction = True
    can_view_comparison = True
    can_view_advanced = True
    can_manage_users = True

    permissions = {}

    context = {
        'user': user,
        'role': role_display,
        'permissions': permissions,
        'can_view_prediction': can_view_prediction,
        'can_view_comparison': can_view_comparison,
        'can_view_advanced': can_view_advanced,
        'can_manage_users': can_manage_users,
    }
    return render(request, 'analysis/index.html', context)

def get_user_context(user):
    """获取用户权限上下文"""

    if user.username == 'admin':
        role_display = '管理员'
    elif user.username == 'analyst':
        role_display = '数据分析师'
    else:
        role_display = '普通用户'

    # 所有用户都有所有权限
    return {
        'role': role_display,
        'can_view_prediction': True,
        'can_view_comparison': True,
        'can_view_advanced': True,
        'can_manage_users': True,
    }

def brand_analysis(request):
    context = get_user_context(request.user)
    return render(request, 'analysis/brand-analysis.html', context)

def price_distribution(request):
    context = get_user_context(request.user)
    return render(request, 'analysis/price-distribution.html', context)

def cpu_analysis(request):
    context = get_user_context(request.user)
    return render(request, 'analysis/cpu-analysis.html', context)

def memory_analysis(request):
    context = get_user_context(request.user)
    return render(request, 'analysis/memory-analysis.html', context)

def get_brand_stats(request):
    # 获取品牌统计数据
    data = Phone.objects.values('brand').annotate(
        avg_price=Avg('price'),
        count=Count('id')
    ).order_by('-count')[:10]  # 获取前10个品牌
    
    brands = [item['brand'] for item in data]
    counts = [item['count'] for item in data]
    avg_prices = [float(item['avg_price']) for item in data]
    
    return JsonResponse({
        'brands': brands,
        'counts': counts,
        'avg_prices': avg_prices
    })

def get_price_distribution(request):
    # 获取价格分布数据
    phones = Phone.objects.all().values('price')
    df = pd.DataFrame(list(phones))
    
    # 定义价格区间
    bins = [0, 1000, 2000, 3000, 4000, float('inf')]
    labels = ['0-1000', '1000-2000', '2000-3000', '3000-4000', '4000+']
    
    df['price_range'] = pd.cut(df['price'], bins=bins, labels=labels)
    distribution = df['price_range'].value_counts().sort_index()
    
    return JsonResponse({
        'ranges': labels,
        'counts': [int(x) for x in distribution.values] 
    })

def get_cpu_stats(request):
    # 获取CPU统计数据
    data = Phone.objects.values('cpu').annotate(
        count=Count('id'),
        avg_price=Avg('price')
    ).order_by('-count')[:10]  # 获取前10个CPU型号

    return JsonResponse({
        'cpu_models': [item['cpu'] for item in data],
        'counts': [int(item['count']) for item in data],
        'avg_prices': [float(item['avg_price']) for item in data]
    })

def get_memory_stats(request):
    # 获取内存统计数据
    data = Phone.objects.values('memory').annotate(
        count=Count('id'),
        avg_price=Avg('price')
    ).order_by('memory')  # 按内存大小排序

    return JsonResponse({
        'memory_sizes': [f"{item['memory']}GB" for item in data],
        'counts': [int(item['count']) for item in data],
        'avg_prices': [float(item['avg_price']) for item in data]
    })

def get_memory_stats(request):
    # 获取内存统计数据
    data = Phone.objects.values('memory').annotate(
        count=Count('id'),
        avg_price=Avg('price')
    ).order_by('memory')  # 按内存大小排序

    return JsonResponse({
        'memory_sizes': [f"{item['memory']}GB" for item in data],
        'counts': [int(item['count']) for item in data],
        'avg_prices': [float(item['avg_price']) for item in data]
    })

def phone_filter(request):
    """手机筛选页面"""
    # 获取所有可用的筛选选项
    brands = Phone.objects.values_list('brand', flat=True).distinct().order_by('brand')
    cpu_models = Phone.objects.values_list('cpu', flat=True).distinct().order_by('cpu')
    memory_sizes = Phone.objects.values_list('memory', flat=True).distinct().order_by('memory')
    colors = Phone.objects.values_list('color', flat=True).distinct().order_by('color')
    os_types = Phone.objects.values_list('os', flat=True).distinct().order_by('os')

    # 获取价格范围
    price_range = Phone.objects.aggregate(
        min_price=Min('price'),
        max_price=Max('price')
    )

    context = get_user_context(request.user)
    context.update({
        'brands': brands,
        'cpu_models': cpu_models,
        'memory_sizes': memory_sizes,
        'colors': colors,
        'os_types': os_types,
        'min_price': price_range['min_price'] or 0,
        'max_price': price_range['max_price'] or 10000,
    })

    return render(request, 'analysis/phone-filter.html', context)

def filter_phones(request):
    """根据筛选条件返回手机数据"""
    try:
        # 获取筛选参数
        brand = request.GET.get('brand', '')
        cpu = request.GET.get('cpu', '')
        memory = request.GET.get('memory', '')
        color = request.GET.get('color', '')
        os_type = request.GET.get('os', '')
        min_price = request.GET.get('min_price', '')
        max_price = request.GET.get('max_price', '')
        rating_min = request.GET.get('rating_min', '')
        rating_max = request.GET.get('rating_max', '')
        page = request.GET.get('page', 1)

        # 构建查询条件
        queryset = Phone.objects.all()

        if brand:
            queryset = queryset.filter(brand__icontains=brand)

        if cpu:
            queryset = queryset.filter(cpu__icontains=cpu)

        if memory:
            queryset = queryset.filter(memory__icontains=memory)

        if color:
            queryset = queryset.filter(color__icontains=color)

        if os_type:
            queryset = queryset.filter(os__icontains=os_type)

        if min_price:
            try:
                queryset = queryset.filter(price__gte=float(min_price))
            except (ValueError, TypeError):
                pass

        if max_price:
            try:
                queryset = queryset.filter(price__lte=float(max_price))
            except (ValueError, TypeError):
                pass

        if rating_min:
            try:
                queryset = queryset.filter(rating__gte=float(rating_min))
            except (ValueError, TypeError):
                pass

        if rating_max:
            try:
                queryset = queryset.filter(rating__lte=float(rating_max))
            except (ValueError, TypeError):
                pass

        # 排序
        sort_by = request.GET.get('sort_by', 'id')
        sort_order = request.GET.get('sort_order', 'asc')

        # 验证排序字段
        valid_sort_fields = ['id', 'brand', 'model', 'price', 'rating', 'memory', 'battery']
        if sort_by not in valid_sort_fields:
            sort_by = 'id'

        if sort_order == 'desc':
            sort_by = f'-{sort_by}'

        queryset = queryset.order_by(sort_by)

        # 分页
        try:
            page = int(page)
        except (ValueError, TypeError):
            page = 1

        paginator = Paginator(queryset, 20)  # 每页显示20条记录
        phones = paginator.get_page(page)

        # 统计信息
        total_count = queryset.count()
        avg_price = queryset.aggregate(avg_price=Avg('price'))['avg_price']
        avg_rating = queryset.aggregate(avg_rating=Avg('rating'))['avg_rating']

        # 如果是AJAX请求，返回JSON数据
        if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
            phones_data = []
            print(f"DEBUG: 查询到 {total_count} 条记录")
            print(f"DEBUG: 当前页有 {len(phones)} 条记录")

            # 定义评分提取函数
            def extract_rating(rating_str):
                if pd.isna(rating_str) or rating_str == '无评价' or not rating_str:
                    return 0
                try:
                    # 如果已经是数值，直接使用
                    if isinstance(rating_str, (int, float)):
                        return float(rating_str)
                    # 提取星级数值，如"9.6星" -> 9.6
                    rating_match = re.search(r'(\d+(?:\.\d+)?)', str(rating_str))
                    if rating_match:
                        rating_value = float(rating_match.group(1))
                        return min(max(rating_value, 0), 10)  # 限制在0-10之间
                    return 0
                except:
                    return 0

            for phone in phones:
                try:
                    phone_data = {
                        'id': phone.id,
                        'brand': phone.brand or '',
                        'model': phone.model or '',
                        'color': phone.color or '',
                        'price': float(phone.price) if phone.price else 0,
                        'cpu': phone.cpu or '',
                        'memory': phone.memory or '',
                        'battery': phone.battery or '',
                        'os': phone.os or '',
                        'camera': phone.camera or '',
                        'rating': extract_rating(phone.rating),
                    }
                    phones_data.append(phone_data)
                    print(f"DEBUG: 添加手机数据: {phone.brand} {phone.model}, 评分: {phone.rating} -> {phone_data['rating']}")
                except Exception as e:
                    print(f"DEBUG: 处理手机数据时出错: {e}")
                    # 如果单个手机数据有问题，跳过
                    continue

            print(f"DEBUG: 最终返回 {len(phones_data)} 条手机数据")

            response_data = {
                'phones': phones_data,
                'total_count': total_count,
                'avg_price': float(avg_price) if avg_price else 0,
                'avg_rating': float(avg_rating) if avg_rating else 0,
                'has_next': phones.has_next(),
                'has_previous': phones.has_previous(),
                'current_page': phones.number,
                'total_pages': phones.paginator.num_pages,
            }

            return JsonResponse(response_data)

        # 普通请求返回HTML页面
        context = {
            'phones': phones,
            'total_count': total_count,
            'avg_price': avg_price,
            'avg_rating': avg_rating,
        }

        return render(request, 'analysis/filter-results.html', context)

    except Exception as e:
        # 如果是AJAX请求，返回错误JSON
        if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
            return JsonResponse({
                'error': True,
                'message': f'筛选失败: {str(e)}',
                'phones': [],
                'total_count': 0,
                'avg_price': 0,
                'avg_rating': 0,
                'has_next': False,
                'has_previous': False,
                'current_page': 1,
                'total_pages': 1,
            })
        else:
            # 普通请求返回错误页面
            return render(request, 'analysis/error.html', {'error': str(e)})

def prediction_analysis(request):
    """预测分析页面"""
    context = get_user_context(request.user)
    return render(request, 'analysis/prediction-analysis.html', context)

def get_price_prediction(request):
    """价格趋势预测"""
    try:
        # 获取所有手机数据
        phones = Phone.objects.all().values('brand', 'price', 'memory', 'rating', 'cpu')
        df = pd.DataFrame(list(phones))

        # 检查数据是否为空
        if df.empty:
            return JsonResponse({
                'success': False,
                'error': '没有可用的手机数据'
            })

        # 数据清洗和预处理
        # 1. 删除价格为空的记录
        df = df.dropna(subset=['price'])

        # 2. 处理评分字段（从"X.X星"格式中提取数值，保持原始数值）
        def extract_rating(rating_str):
            if pd.isna(rating_str) or rating_str == '无评价':
                return 8.0  # 默认评分（10星制）
            try:
                # 提取星级数值，如"9.6星" -> 9.6，保持原始数值
                rating_match = re.search(r'(\d+(?:\.\d+)?)', str(rating_str))
                if rating_match:
                    rating_value = float(rating_match.group(1))
                    # 保持原始评分，不进行转换
                    return min(max(rating_value, 0), 10)  # 限制在0-10之间
                return 8.0
            except:
                return 8.0

        if 'rating' in df.columns:
            df['rating'] = df['rating'].apply(extract_rating)
        else:
            df['rating'] = 4.0  # 默认评分

        # 3. 处理品牌和CPU缺失值
        df['brand'] = df['brand'].fillna('Unknown')
        df['cpu'] = df['cpu'].fillna('Unknown')
        df['memory'] = df['memory'].fillna('4GB')

        # 4. 提取内存数值（处理各种格式）
        df['memory_str'] = df['memory'].astype(str)
        df['memory_numeric'] = df['memory_str'].str.extract('(\d+)').astype(float)

        # 如果提取失败，设置默认值
        df['memory_numeric'] = df['memory_numeric'].fillna(4.0)

        # 5. 确保所有数值列没有NaN
        df['price'] = pd.to_numeric(df['price'], errors='coerce')
        df['rating'] = pd.to_numeric(df['rating'], errors='coerce')
        df['memory_numeric'] = pd.to_numeric(df['memory_numeric'], errors='coerce')

        # 再次删除转换后仍有NaN的记录
        df = df.dropna(subset=['price', 'rating', 'memory_numeric'])

        # 检查处理后是否还有足够的数据
        if len(df) < 10:
            return JsonResponse({
                'success': False,
                'error': f'可用数据太少，只有{len(df)}条记录'
            })

        # 特征工程
        le_brand = LabelEncoder()
        le_cpu = LabelEncoder()

        df['brand_encoded'] = le_brand.fit_transform(df['brand'])
        df['cpu_encoded'] = le_cpu.fit_transform(df['cpu'])

        # 准备特征和目标变量
        features = ['brand_encoded', 'cpu_encoded', 'memory_numeric', 'rating']
        X = df[features]
        y = df['price']

        # 最终检查是否有NaN值
        if X.isnull().any().any() or y.isnull().any():
            return JsonResponse({
                'success': False,
                'error': '数据预处理后仍存在缺失值'
            })

        # 训练模型
        X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.2, random_state=42)
        model = LinearRegression()
        model.fit(X_train, y_train)

        # 预测
        y_pred = model.predict(X_test)

        # 计算模型性能
        mse = mean_squared_error(y_test, y_pred)
        r2 = r2_score(y_test, y_pred)

        # 品牌价格趋势预测
        brand_predictions = []
        unique_brands = df['brand'].unique()[:10]  # 取前10个品牌
        unique_cpus = df['cpu'].unique()[:5]  # 取前5个CPU

        for brand in unique_brands:
            brand_data = df[df['brand'] == brand]
            if len(brand_data) > 0:
                avg_memory = brand_data['memory_numeric'].mean()
                avg_rating = brand_data['rating'].mean()

                # 确保没有NaN值
                if pd.isna(avg_memory) or pd.isna(avg_rating):
                    continue

                try:
                    brand_encoded = le_brand.transform([brand])[0]
                except:
                    continue

                # 预测不同CPU下的价格
                cpu_predictions = []
                for cpu in unique_cpus:
                    try:
                        cpu_encoded = le_cpu.transform([cpu])[0]
                        prediction_input = [[brand_encoded, cpu_encoded, avg_memory, avg_rating]]

                        # 检查输入是否有NaN
                        if not np.isnan(prediction_input).any():
                            predicted_price = model.predict(prediction_input)[0]
                            if not np.isnan(predicted_price) and predicted_price > 0:
                                cpu_predictions.append({
                                    'cpu': cpu,
                                    'predicted_price': float(predicted_price)
                                })
                    except Exception as e:
                        continue

                if cpu_predictions:  # 只有当有有效预测时才添加
                    brand_predictions.append({
                        'brand': brand,
                        'current_avg_price': float(brand_data['price'].mean()),
                        'cpu_predictions': cpu_predictions
                    })

        # 生成未来价格趋势（模拟）
        future_trends = []
        brand_price_data = df.groupby('brand')['price'].agg(['mean', 'std']).head(5)

        # 设置随机种子以获得一致的结果
        np.random.seed(42)

        for brand, row in brand_price_data.iterrows():
            base_price = row['mean']
            price_std = row['std'] if not pd.isna(row['std']) else base_price * 0.1

            # 确保基础价格有效
            if pd.isna(base_price) or base_price <= 0:
                continue

            trend_data = []
            for month in range(1, 13):  # 未来12个月
                try:
                    # 模拟价格波动（基于历史数据的简单趋势）
                    seasonal_factor = 1 + 0.1 * np.sin(month * np.pi / 6)  # 季节性因素
                    trend_factor = 1 + (month * 0.01)  # 轻微上升趋势
                    noise = np.random.normal(0, 0.03)  # 减少随机噪声

                    predicted_price = base_price * seasonal_factor * trend_factor * (1 + noise)

                    # 确保价格在合理范围内
                    predicted_price = max(predicted_price, base_price * 0.5)  # 不低于原价的50%
                    predicted_price = min(predicted_price, base_price * 2.0)  # 不高于原价的200%

                    trend_data.append({
                        'month': month,
                        'price': float(predicted_price)
                    })
                except Exception as e:
                    # 如果计算失败，使用基础价格
                    trend_data.append({
                        'month': month,
                        'price': float(base_price)
                    })

            if trend_data:  # 只有当有有效数据时才添加
                future_trends.append({
                    'brand': brand,
                    'trend_data': trend_data
                })

        return JsonResponse({
            'success': True,
            'model_performance': {
                'mse': float(mse),
                'r2_score': float(r2),
                'accuracy': f"{r2 * 100:.1f}%"
            },
            'brand_predictions': brand_predictions,
            'future_trends': future_trends
        })

    except Exception as e:
        return JsonResponse({
            'success': False,
            'error': str(e)
        })

def get_brand_trend_prediction(request):
    """品牌热度趋势预测"""
    try:
        # 获取品牌统计数据
        brand_stats = Phone.objects.values('brand').annotate(
            count=Count('id'),
            avg_price=Avg('price'),
            avg_rating=Avg('rating')
        ).order_by('-count')

        df = pd.DataFrame(list(brand_stats))

        # 检查数据是否为空
        if df.empty:
            return JsonResponse({
                'success': False,
                'error': '没有可用的品牌数据'
            })

        # 数据清洗和类型转换
        # 将Decimal类型转换为float类型
        df['avg_price'] = pd.to_numeric(df['avg_price'], errors='coerce')

        # 处理平均评分（可能包含星级格式，保持原始数值）
        def extract_avg_rating(rating_str):
            if pd.isna(rating_str):
                return 8.0
            try:
                # 如果已经是数值，直接使用
                if isinstance(rating_str, (int, float)):
                    rating_value = float(rating_str)
                else:
                    # 尝试提取星级数值
                    rating_match = re.search(r'(\d+(?:\.\d+)?)', str(rating_str))
                    if rating_match:
                        rating_value = float(rating_match.group(1))
                    else:
                        return 8.0

                # 保持原始评分，不进行转换
                return min(max(rating_value, 0), 10)
            except:
                return 8.0

        df['avg_rating'] = df['avg_rating'].apply(extract_avg_rating)
        df['count'] = pd.to_numeric(df['count'], errors='coerce')

        # 处理缺失值
        df['avg_price'] = df['avg_price'].fillna(df['avg_price'].mean() if not df['avg_price'].isnull().all() else 2000.0)
        df['avg_rating'] = df['avg_rating'].fillna(df['avg_rating'].mean() if not df['avg_rating'].isnull().all() else 4.0)
        df['count'] = df['count'].fillna(1.0)

        # 确保没有NaN值
        df = df.dropna()

        # 检查处理后是否还有数据
        if df.empty:
            return JsonResponse({
                'success': False,
                'error': '数据处理后没有可用记录'
            })

        # 计算品牌热度指数（综合销量、价格、评分，适应10星制）
        try:
            # 确保所有计算都使用float类型
            count_score = df['count'].astype(float) * 0.5  # 销量权重50%
            rating_score = (df['avg_rating'].astype(float) / 10.0) * 30.0  # 10星制评分权重30%

            # 性价比计算（避免除零错误）
            price_normalized = df['avg_price'].astype(float) / 1000.0
            price_normalized = price_normalized.replace(0, 0.001)  # 避免除零
            price_score = (1.0 / price_normalized) * 20.0  # 性价比权重20%

            df['popularity_score'] = count_score + rating_score + price_score

        except Exception as e:
            # 如果计算失败，使用简化的热度指数
            df['popularity_score'] = df['count'].astype(float)

        # 预测未来品牌热度
        brand_predictions = []
        # 设置随机种子以获得一致的结果
        np.random.seed(42)

        for _, row in df.head(10).iterrows():
            try:
                # 模拟未来12个月的热度变化
                future_popularity = []
                base_score = float(row['popularity_score'])

                # 确保基础分数有效
                if pd.isna(base_score) or base_score <= 0:
                    base_score = 1.0

                for month in range(1, 13):
                    try:
                        # 基于当前热度和市场趋势预测
                        market_trend = 1.0 + (month * 0.01)  # 整体市场增长
                        brand_factor = 1.0 + np.random.normal(0, 0.05)  # 品牌特定因素（减少波动）
                        seasonal_factor = 1.0 + 0.1 * np.sin(month * np.pi / 6)  # 季节性（减少波动）

                        predicted_score = base_score * market_trend * brand_factor * seasonal_factor

                        # 确保预测分数在合理范围内
                        predicted_score = max(predicted_score, base_score * 0.5)
                        predicted_score = min(predicted_score, base_score * 2.0)

                        future_popularity.append({
                            'month': month,
                            'popularity_score': float(predicted_score),
                            'rank_prediction': 'rising' if predicted_score > base_score else 'stable'
                        })
                    except Exception as e:
                        # 如果单月计算失败，使用基础分数
                        future_popularity.append({
                            'month': month,
                            'popularity_score': float(base_score),
                            'rank_prediction': 'stable'
                        })

                brand_predictions.append({
                    'brand': str(row['brand']),
                    'current_popularity': float(base_score),
                    'current_count': int(float(row['count'])),
                    'current_avg_price': float(row['avg_price']),
                    'current_avg_rating': float(row['avg_rating']),
                    'future_trend': future_popularity
                })

            except Exception as e:
                # 如果整个品牌处理失败，跳过该品牌
                continue

        # 计算市场份额预测
        if brand_predictions:
            try:
                total_count = sum([item['current_count'] for item in brand_predictions])
                if total_count > 0:
                    for item in brand_predictions:
                        try:
                            item['current_market_share'] = float((item['current_count'] / total_count) * 100)

                            # 预测未来市场份额
                            future_share = []
                            current_popularity = float(item['current_popularity'])
                            current_share = float(item['current_market_share'])

                            for month in range(1, 13):
                                try:
                                    # 基于热度变化预测市场份额变化
                                    if current_popularity > 0:
                                        popularity_change = float(item['future_trend'][month-1]['popularity_score']) / current_popularity
                                        predicted_share = current_share * popularity_change
                                        predicted_share = min(max(predicted_share, 0), 50)  # 限制在0-50%之间
                                    else:
                                        predicted_share = current_share

                                    future_share.append({
                                        'month': month,
                                        'market_share': float(predicted_share)
                                    })
                                except Exception as e:
                                    # 如果单月计算失败，使用当前份额
                                    future_share.append({
                                        'month': month,
                                        'market_share': float(current_share)
                                    })

                            item['future_market_share'] = future_share
                        except Exception as e:
                            # 如果品牌份额计算失败，设置默认值
                            item['current_market_share'] = 0.0
                            item['future_market_share'] = [{'month': m, 'market_share': 0.0} for m in range(1, 13)]
                else:
                    # 如果总数为0，设置所有份额为0
                    for item in brand_predictions:
                        item['current_market_share'] = 0.0
                        item['future_market_share'] = [{'month': m, 'market_share': 0.0} for m in range(1, 13)]
            except Exception as e:
                # 如果整个市场份额计算失败，设置默认值
                for item in brand_predictions:
                    item['current_market_share'] = 0.0
                    item['future_market_share'] = [{'month': m, 'market_share': 0.0} for m in range(1, 13)]

        return JsonResponse({
            'success': True,
            'brand_predictions': brand_predictions,
            'market_insights': {
                'total_brands': len(brand_predictions),
                'top_brand': brand_predictions[0]['brand'] if brand_predictions else None,
                'prediction_period': '12 months'
            }
        })

    except Exception as e:
        return JsonResponse({
            'success': False,
            'error': str(e)
        })

def price_comparison(request):
    """同价位机型对比页面"""
    context = get_user_context(request.user)
    return render(request, 'analysis/price-comparison.html', context)
def get_price_range_analysis(request):
    """获取价格区间分析数据"""
    try:
        # 获取分页参数
        page = int(request.GET.get('page', 1))
        page_size = int(request.GET.get('page_size', 10))
        range_name = request.GET.get('range_name', '')

        # 获取所有手机数据
        phones = Phone.objects.all().values('id', 'brand', 'model', 'price', 'cpu', 'memory', 'battery', 'rating', 'camera', 'color', 'os')
        df = pd.DataFrame(list(phones))

        if df.empty:
            return JsonResponse({
                'success': False,
                'error': '没有可用的手机数据'
            })

        # 数据清洗
        df = df.dropna(subset=['price'])
        df['price'] = pd.to_numeric(df['price'], errors='coerce')

        # 过滤价格为0的手机
        df = df[df['price'] > 0]

        # 处理评分字段（从"X.X星"格式中提取数值，保持原始数值）
        def extract_rating(rating_str):
            if pd.isna(rating_str) or rating_str == '无评价':
                return 8.0
            try:
                rating_match = re.search(r'(\d+(?:\.\d+)?)', str(rating_str))
                if rating_match:
                    rating_value = float(rating_match.group(1))
                    # 保持原始评分，不进行转换
                    return min(max(rating_value, 0), 10)
                return 8.0
            except:
                return 8.0

        df['rating'] = df['rating'].apply(extract_rating)

        # 提取数值特征
        df['memory_numeric'] = df['memory'].astype(str).str.extract('(\d+)').astype(float).fillna(4.0)
        df['battery_numeric'] = df['battery'].astype(str).str.extract('(\d+)').astype(float).fillna(3000.0)

        # 处理摄像头参数（提取主摄像头像素）
        def extract_camera_mp(camera_str):
            if pd.isna(camera_str):
                return 12.0
            try:
                # 查找数字+MP的模式
                matches = re.findall(r'(\d+(?:\.\d+)?)\s*MP', str(camera_str), re.IGNORECASE)
                if matches:
                    return float(max(matches))  # 取最大的像素值
                # 查找纯数字
                matches = re.findall(r'(\d+(?:\.\d+)?)', str(camera_str))
                if matches:
                    return float(max(matches))
                return 12.0
            except:
                return 12.0

        df['camera_mp'] = df['camera'].apply(extract_camera_mp)

        # 定义价格区间
        price_ranges = [
            (0, 1000, '0-1000元'),
            (1000, 2000, '1000-2000元'),
            (2000, 3000, '2000-3000元'),
            (3000, 4000, '3000-4000元'),
            (4000, 6000, '4000-6000元'),
            (6000, float('inf'), '6000元以上')
        ]

        range_analysis = []

        for min_price, max_price, range_name_key in price_ranges:
            if max_price == float('inf'):
                range_phones = df[df['price'] >= min_price]
            else:
                range_phones = df[(df['price'] >= min_price) & (df['price'] < max_price)]

            if len(range_phones) == 0:
                continue

            # 计算该价格区间的统计信息
            stats = {
                'range_name': range_name_key,
                'min_price': min_price,
                'max_price': max_price if max_price != float('inf') else 10000,
                'phone_count': len(range_phones),
                'avg_price': float(range_phones['price'].mean()),
                'avg_rating': float(range_phones['rating'].mean()),
                'avg_memory': float(range_phones['memory_numeric'].mean()),
                'avg_battery': float(range_phones['battery_numeric'].mean()),
                'avg_camera': float(range_phones['camera_mp'].mean()),
                'top_brands': range_phones['brand'].value_counts().head(3).to_dict(),
                'phones': []
            }

            # 如果指定了特定价格区间，进行分页处理
            if range_name and range_name == range_name_key:
                # 按综合评分排序
                range_phones_sorted = range_phones.copy()

                # 计算综合评分
                for idx, phone in range_phones_sorted.iterrows():
                    try:
                        price_score = (max_price - phone['price']) / (max_price - min_price) * 25 if max_price > min_price else 0
                        rating_score = (phone['rating'] / 10) * 30
                        memory_score = min(phone['memory_numeric'] / 16 * 20, 20)
                        battery_score = min(phone['battery_numeric'] / 5000 * 15, 15)
                        camera_score = min(phone['camera_mp'] / 100 * 10, 10)

                        total_score = price_score + rating_score + memory_score + battery_score + camera_score
                        range_phones_sorted.loc[idx, 'total_score'] = total_score
                    except:
                        range_phones_sorted.loc[idx, 'total_score'] = 0

                # 按评分排序
                range_phones_sorted = range_phones_sorted.sort_values('total_score', ascending=False)

                # 分页处理
                total_phones = len(range_phones_sorted)
                start_idx = (page - 1) * page_size
                end_idx = start_idx + page_size
                paginated_phones = range_phones_sorted.iloc[start_idx:end_idx]

                # 获取分页后的手机详情
                phones_list = []
                for _, phone in paginated_phones.iterrows():
                    phone_info = {
                        'id': int(phone['id']),
                        'brand': str(phone['brand']),
                        'model': str(phone['model']),
                        'price': float(phone['price']),
                        'rating': float(phone['rating']),
                        'memory': float(phone['memory_numeric']),
                        'battery': float(phone['battery_numeric']),
                        'camera': float(phone['camera_mp']),
                        'cpu': str(phone['cpu']),
                        'color': str(phone['color']),
                        'os': str(phone['os']),
                        'total_score': float(phone.get('total_score', 0))
                    }
                    phones_list.append(phone_info)

                stats['phones'] = phones_list
                stats['pagination'] = {
                    'current_page': page,
                    'page_size': page_size,
                    'total_phones': total_phones,
                    'total_pages': (total_phones + page_size - 1) // page_size,
                    'has_previous': page > 1,
                    'has_next': page * page_size < total_phones
                }
            else:
                # 不分页，只返回前10个手机作为预览
                preview_phones = range_phones.head(10)
                phones_list = []
                for _, phone in preview_phones.iterrows():
                    phone_info = {
                        'id': int(phone['id']),
                        'brand': str(phone['brand']),
                        'model': str(phone['model']),
                        'price': float(phone['price']),
                        'rating': float(phone['rating']),
                        'memory': float(phone['memory_numeric']),
                        'battery': float(phone['battery_numeric']),
                        'camera': float(phone['camera_mp']),
                        'cpu': str(phone['cpu']),
                        'color': str(phone['color']),
                        'os': str(phone['os'])
                    }
                    phones_list.append(phone_info)

                stats['phones'] = phones_list
                stats['pagination'] = None

            range_analysis.append(stats)

        return JsonResponse({
            'success': True,
            'price_ranges': range_analysis
        })

    except Exception as e:
        return JsonResponse({
            'success': False,
            'error': str(e)
        })

def get_similar_phones(request):
    """使用K-Means聚类找出相似机型"""
    try:
        phone_id = request.GET.get('phone_id')
        if not phone_id:
            return JsonResponse({
                'success': False,
                'error': '请提供手机ID'
            })

        # 获取目标手机信息
        try:
            target_phone = Phone.objects.get(id=phone_id)
        except Phone.DoesNotExist:
            return JsonResponse({
                'success': False,
                'error': '手机不存在'
            })

        # 获取所有手机数据
        phones = Phone.objects.all().values('id', 'brand', 'model', 'price', 'cpu', 'memory', 'battery', 'rating', 'camera')
        df = pd.DataFrame(list(phones))

        if df.empty:
            return JsonResponse({
                'success': False,
                'error': '没有可用的手机数据'
            })

        # 数据预处理
        df = df.dropna(subset=['price'])
        df['price'] = pd.to_numeric(df['price'], errors='coerce')

        # 过滤价格为0的手机
        df = df[df['price'] > 0]

        # 处理评分字段（从"X.X星"格式中提取数值，保持原始数值）
        def extract_rating(rating_str):
            if pd.isna(rating_str) or rating_str == '无评价':
                return 8.0
            try:
                rating_match = re.search(r'(\d+(?:\.\d+)?)', str(rating_str))
                if rating_match:
                    rating_value = float(rating_match.group(1))
                    # 保持原始评分，不进行转换
                    return min(max(rating_value, 0), 10)
                return 8.0
            except:
                return 8.0

        df['rating'] = df['rating'].apply(extract_rating)

        # 提取数值特征
        df['memory_numeric'] = df['memory'].astype(str).str.extract('(\d+)').astype(float).fillna(4.0)
        df['battery_numeric'] = df['battery'].astype(str).str.extract('(\d+)').astype(float).fillna(3000.0)

        def extract_camera_mp(camera_str):
            if pd.isna(camera_str):
                return 12.0
            try:
                matches = re.findall(r'(\d+(?:\.\d+)?)\s*MP', str(camera_str), re.IGNORECASE)
                if matches:
                    return float(max(matches))
                matches = re.findall(r'(\d+(?:\.\d+)?)', str(camera_str))
                if matches:
                    return float(max(matches))
                return 12.0
            except:
                return 12.0

        df['camera_mp'] = df['camera'].apply(extract_camera_mp)

        # 准备聚类特征
        features = ['price', 'rating', 'memory_numeric', 'battery_numeric', 'camera_mp']
        X = df[features].fillna(df[features].mean())

        # 标准化特征
        scaler = StandardScaler()
        X_scaled = scaler.fit_transform(X)

        # K-Means聚类
        n_clusters = min(8, len(df) // 3)  # 动态确定聚类数量
        if n_clusters < 2:
            n_clusters = 2

        kmeans = KMeans(n_clusters=n_clusters, random_state=42)
        cluster_labels = kmeans.fit_predict(X_scaled)
        df['cluster'] = cluster_labels

        # 重置DataFrame索引以确保连续性
        df_reset = df.reset_index(drop=True)

        # 检查数据一致性
        if len(df_reset) != len(X_scaled):
            return JsonResponse({
                'success': False,
                'error': f'数据不一致：DataFrame长度{len(df_reset)}，特征矩阵长度{len(X_scaled)}'
            })

        # 找到目标手机的聚类
        target_mask = df_reset['id'] == int(phone_id)
        if not target_mask.any():
            return JsonResponse({
                'success': False,
                'error': '目标手机不存在'
            })

        target_index = df_reset[target_mask].index[0]
        target_cluster = df_reset.loc[target_index, 'cluster']

        # 获取同一聚类的手机
        similar_phones = df_reset[df_reset['cluster'] == target_cluster]

        # 计算与目标手机的相似度（基于欧氏距离）
        target_features = X_scaled[target_index]

        similarities = []
        for idx, phone in similar_phones.iterrows():
            if phone['id'] == int(phone_id):
                continue  # 跳过目标手机本身

            # 检查索引是否在有效范围内
            if idx >= len(X_scaled):
                continue  # 跳过无效索引

            phone_features = X_scaled[idx]
            distance = np.linalg.norm(target_features - phone_features)
            similarity = 1 / (1 + distance)  # 转换为相似度分数

            similarities.append({
                'id': int(phone['id']),
                'brand': str(phone['brand']),
                'model': str(phone['model']),
                'price': float(phone['price']),
                'rating': float(phone['rating']),
                'memory': float(phone['memory_numeric']),
                'battery': float(phone['battery_numeric']),
                'camera': float(phone['camera_mp']),
                'similarity_score': float(similarity)
            })

        # 按相似度排序
        similarities.sort(key=lambda x: x['similarity_score'], reverse=True)

        # 处理目标手机的评分
        def extract_single_rating(rating_str):
            if pd.isna(rating_str) or rating_str == '无评价':
                return 8.0
            try:
                rating_match = re.search(r'(\d+(?:\.\d+)?)', str(rating_str))
                if rating_match:
                    rating_value = float(rating_match.group(1))
                    return min(max(rating_value, 0), 10)
                return 8.0
            except:
                return 8.0

        target_rating = extract_single_rating(target_phone.rating)
        target_price = float(target_phone.price) if target_phone.price else 0

        return JsonResponse({
            'success': True,
            'target_phone': {
                'id': target_phone.id,
                'brand': target_phone.brand,
                'model': target_phone.model,
                'price': target_price,
                'rating': target_rating
            },
            'similar_phones': similarities[:10]  # 返回前10个最相似的手机
        })

    except Exception as e:
        return JsonResponse({
            'success': False,
            'error': str(e)
        })
