#!/usr/bin/env python
"""
简单的数据库初始化脚本 - 直接使用SQL创建表和数据
"""
import pymysql
import json
from datetime import datetime

def create_tables_and_data():
    """创建表和初始数据"""
    try:
        # 连接数据库
        connection = pymysql.connect(
            host='localhost',
            user='root',
            password='root',
            database='phone_db',
            charset='utf8mb4'
        )
        
        cursor = connection.cursor()
        
        # 1. 创建角色表
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS `user_roles` (
                `id` bigint NOT NULL AUTO_INCREMENT,
                `name` varchar(20) NOT NULL UNIQUE,
                `display_name` varchar(50) NOT NULL,
                `description` longtext NOT NULL,
                `permissions` json NOT NULL,
                `created_at` datetime(6) NOT NULL,
                PRIMARY KEY (`id`)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        """)
        
        # 2. 创建用户表
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS `users` (
                `id` bigint NOT NULL AUTO_INCREMENT,
                `password` varchar(128) NOT NULL,
                `last_login` datetime(6) DEFAULT NULL,
                `is_superuser` tinyint(1) NOT NULL,
                `username` varchar(150) NOT NULL UNIQUE,
                `first_name` varchar(150) NOT NULL,
                `last_name` varchar(150) NOT NULL,
                `email` varchar(254) NOT NULL,
                `is_staff` tinyint(1) NOT NULL,
                `is_active` tinyint(1) NOT NULL,
                `date_joined` datetime(6) NOT NULL,
                `phone` varchar(20) NOT NULL,
                `department` varchar(100) NOT NULL,
                `last_login_ip` char(39) DEFAULT NULL,
                `created_at` datetime(6) NOT NULL,
                `updated_at` datetime(6) NOT NULL,
                `role_id` bigint DEFAULT NULL,
                PRIMARY KEY (`id`),
                KEY `users_role_id_fk` (`role_id`),
                CONSTRAINT `users_role_id_fk` FOREIGN KEY (`role_id`) REFERENCES `user_roles` (`id`) ON DELETE SET NULL
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        """)
        
        # 3. 创建登录日志表
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS `user_login_logs` (
                `id` bigint NOT NULL AUTO_INCREMENT,
                `login_time` datetime(6) NOT NULL,
                `ip_address` char(39) NOT NULL,
                `user_agent` longtext NOT NULL,
                `login_result` tinyint(1) NOT NULL,
                `user_id` bigint NOT NULL,
                PRIMARY KEY (`id`),
                KEY `user_login_logs_user_id_fk` (`user_id`),
                KEY `user_login_logs_login_time_idx` (`login_time`),
                CONSTRAINT `user_login_logs_user_id_fk` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        """)
        
        # 4. 插入角色数据
        roles_data = [
            ('admin', '管理员', '系统管理员，拥有所有权限', {
                "view_dashboard": True, "view_basic_analysis": True, "view_advanced_analysis": True,
                "view_prediction": True, "view_comparison": True, "export_data": True,
                "manage_users": True, "manage_roles": True, "manage_data": True,
                "view_logs": True, "system_config": True
            }),
            ('analyst', '数据分析师', '数据分析师，拥有高级分析权限', {
                "view_dashboard": True, "view_basic_analysis": True, "view_advanced_analysis": True,
                "view_prediction": True, "view_comparison": True, "export_data": True,
                "manage_users": False, "manage_roles": False, "manage_data": False,
                "view_logs": False, "system_config": False
            }),
            ('user', '普通用户', '普通用户，只能查看基础分析功能', {
                "view_dashboard": True, "view_basic_analysis": True, "view_advanced_analysis": False,
                "view_prediction": False, "view_comparison": False, "export_data": False,
                "manage_users": False, "manage_roles": False, "manage_data": False,
                "view_logs": False, "system_config": False
            })
        ]
        
        for name, display_name, description, permissions in roles_data:
            cursor.execute("""
                INSERT IGNORE INTO `user_roles` (`name`, `display_name`, `description`, `permissions`, `created_at`)
                VALUES (%s, %s, %s, %s, %s)
            """, (name, display_name, description, json.dumps(permissions), datetime.now()))
        
        # 5. 插入用户数据（使用简单密码哈希，实际应用中应该用Django的哈希）
        users_data = [
            ('admin', 'pbkdf2_sha256$260000$test$hash', '<EMAIL>', '系统', '管理员', 1, 1, 'admin'),
            ('analyst', 'pbkdf2_sha256$260000$test$hash', '<EMAIL>', '数据', '分析师', 0, 0, 'analyst'),
            ('user', 'pbkdf2_sha256$260000$test$hash', '<EMAIL>', '普通', '用户', 0, 0, 'user')
        ]
        
        for username, password, email, first_name, last_name, is_superuser, is_staff, role_name in users_data:
            # 获取角色ID
            cursor.execute("SELECT id FROM user_roles WHERE name = %s", (role_name,))
            role_result = cursor.fetchone()
            if role_result:
                role_id = role_result[0]
                cursor.execute("""
                    INSERT IGNORE INTO `users` 
                    (`username`, `password`, `email`, `first_name`, `last_name`, 
                     `is_superuser`, `is_staff`, `is_active`, `date_joined`, 
                     `phone`, `department`, `created_at`, `updated_at`, `role_id`)
                    VALUES (%s, %s, %s, %s, %s, %s, %s, 1, %s, '', '', %s, %s, %s)
                """, (username, password, email, first_name, last_name, 
                      is_superuser, is_staff, datetime.now(), datetime.now(), datetime.now(), role_id))
        
        connection.commit()
        cursor.close()
        connection.close()
        
        print("✅ 数据库表和初始数据创建完成")
        print("\n📝 默认用户账号:")
        print("   管理员: admin / admin123")
        print("   分析师: analyst / analyst123") 
        print("   普通用户: user / user123")
        print("\n⚠️  注意：密码需要在Django中重新设置")
        
        return True
        
    except Exception as e:
        print(f"❌ 数据库初始化失败: {e}")
        return False

if __name__ == '__main__':
    print("🚀 开始初始化数据库...")
    create_tables_and_data()
