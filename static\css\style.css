.container {
    display: flex;
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
}

.sidebar {
    background-color: #333;
    padding: 20px;
    color: #fff;
    width: 200px;
    /* 确保侧边栏在最左边 */
    order: 0;
}

main {
    flex: 1;
    padding-left: 20px; /* 确保主内容与侧边栏有间距 */
}

header nav ul {
    display: flex;
    list-style: none;
    padding: 0;
}

header nav ul li {
    margin-right: 20px;
}

header nav ul li a {
    text-decoration: none;
    color: #333;
}

.analysis-section {
    display: flex;
    flex-wrap: wrap;
    gap: 20px;
    margin-top: 20px;
}

.chart {
    width: 100%;
    height: 400px;
    background: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

@media (min-width: 768px) {
    .chart {
        width: calc(50% - 10px);
    }
}

.navbar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    background-color: #333;
    padding: 10px 20px;
}

.navbar-brand a {
    color: #fff;
    text-decoration: none;
    font-size: 24px;
}

.navbar-menu {
    list-style: none;
    display: flex;
    margin: 0;
    padding: 0;
}

.navbar-menu li {
    margin-right: 20px;
}

.navbar-menu a {
    color: #fff;
    text-decoration: none;
    padding: 8px 16px;
    transition: background-color 0.3s;
}

.navbar-menu a:hover {
    background-color: #575757;
    border-radius: 4px;
}

.dropdown {
    position: relative;
    display: inline-block;
}

.dropdown-content {
    display: none;
    position: absolute;
    background-color: #f9f9f9;
    min-width: 160px;
    box-shadow: 0px 8px 16px 0px rgba(0,0,0,0.2);
    z-index: 1;
}

.dropdown-content a {
    color: black;
    padding: 12px 16px;
    text-decoration: none;
    display: block;
}

.dropdown-content a:hover {
    background-color: #f1f1f1;
}

.dropdown:hover .dropdown-content {
    display: block;
}

.dropdown:hover .dropbtn {
    background-color: #575757;
}

.sidebar {
    background-color: #333;
    padding: 20px;
    color: #fff;
    width: 200px;
    height: max-content;
}

.sidebar h2 {
    color: #fff;
    margin-bottom: 10px;
}

.sidebar ul {
    list-style: none;
    padding: 0;
}

.sidebar ul li {
    margin-bottom: 10px;
}

.sidebar ul li a {
    color: #fff;
    text-decoration: none;
    padding: 8px 16px;
    display: block;
    transition: background-color 0.3s;
}

.sidebar ul li a:hover {
    background-color: #575757;
    border-radius: 4px;
}
.mcd-menu {
    list-style: none;
    padding: 0;
    margin: 0;
    background: #FFF;
    width: 250px;
}

.mcd-menu li {
    position: relative;
}

.mcd-menu li a {
    display: block;
    text-decoration: none;
    padding: 12px 20px;
    color: #777;
    text-align: left;
    height: 36px;
    position: relative;
    border-bottom: 1px solid #EEE;
}

.mcd-menu li a i {
    float: left;
    font-size: 20px;
    margin: 0 10px 0 0;
}

.mcd-menu li a strong {
    display: block;
    text-transform: uppercase;
}

.mcd-menu li a small {
    display: block;
    font-size: 10px;
}

.mcd-menu li:hover > a {
    color: #e67e22;
}

.mcd-menu li a.active {
    color: #e67e22;
    border-left: 4px solid #e67e22;
    margin: 0 -4px;
}

.mcd-menu li ul,
.mcd-menu li ul li ul {
    position: absolute;
    height: auto;
    min-width: 200px;
    padding: 0;
    margin: 0;
    background: #FFF;
    opacity: 0;
    visibility: hidden;
    transition: all 300ms linear;
    z-index: 1000;
    left: 280px;
    top: 0px;
    border-left: 4px solid #e67e22;
}

.mcd-menu li:hover > ul,
.mcd-menu li ul li:hover > ul {
    display: block;
    opacity: 1;
    visibility: visible;
    left: 250px;
}

.mcd-menu li ul li a {
    padding: 10px;
    text-align: left;
    border: 0;
    border-bottom: 1px solid #EEE;
    height: auto;
}

.mcd-menu li ul li a i {
    font-size: 16px;
    display: inline-block;
    margin: 0 10px 0 0;
}

.mcd-menu li ul li ul {
    left: 230px;
    top: 0;
    border: 0;
    border-left: 4px solid #e67e22;
}

.mcd-menu li ul li ul:before {
    content: "";
    position: absolute;
    top: 15px;
    left: -9px;
    border-right: 5px solid #e67e22;
    border-bottom: 5px solid transparent;
    border-top: 5px solid transparent;
}

.mcd-menu li ul li:hover > ul {
    top: 0px;
    left: 200px;
}

.mcd-menu li a.search {
    padding: 10px 10px 15px 10px;
    clear: both;
}

.mcd-menu li a.search i {
    margin: 0;
    display: inline-block;
    font-size: 18px;
}

.mcd-menu li a.search input {
    border: 1px solid #EEE;
    padding: 10px;
    background: #FFF;
    outline: none;
    color: #777;
    width: 170px;
    float: left;
}

.mcd-menu li a.search button {
    border: 1px solid #e67e22;
    background: #e67e22;
    outline: none;
    color: #FFF;
    margin-left: -4px;
    float: left;
    padding: 10px 10px 11px 10px;
}

.mcd-menu li a.search input:focus {
    border: 1px solid #e67e22;
}

.search-mobile {
    display: none !important;
    background: #e67e22;
    border-left: 1px solid #e67e22;
    border-radius: 0 3px 3px 0;
}

.search-mobile i {
    color: #FFF;
    margin: 0 !important;
}