#!/usr/bin/env python
"""
设置数据库和创建初始用户的脚本
"""
import os
import sys
import django
import pymysql

# 添加项目路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# 设置Django环境
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'phone_analysis.settings')
django.setup()

from django.contrib.auth.hashers import make_password
from analysis.models import Role, CustomUser
from analysis.decorators import ROLE_PERMISSIONS

def execute_sql_file():
    """执行SQL文件创建表结构"""
    try:
        # 连接数据库
        connection = pymysql.connect(
            host='localhost',
            user='root',
            password='root',
            database='phone_db',
            charset='utf8mb4'
        )
        
        cursor = connection.cursor()
        
        # 读取SQL文件
        sql_file_path = os.path.join(os.path.dirname(__file__), 'create_tables.sql')
        with open(sql_file_path, 'r', encoding='utf-8') as file:
            sql_content = file.read()
        
        # 分割SQL语句并执行
        sql_statements = sql_content.split(';')
        for statement in sql_statements:
            statement = statement.strip()
            if statement and not statement.startswith('--'):
                try:
                    cursor.execute(statement)
                except Exception as e:
                    if "already exists" not in str(e) and "Duplicate entry" not in str(e):
                        print(f"⚠️  SQL执行警告: {e}")
        
        connection.commit()
        cursor.close()
        connection.close()
        
        print("✅ 数据库表结构创建完成")
        return True
        
    except Exception as e:
        print(f"❌ 数据库表创建失败: {e}")
        return False

def create_roles_and_users():
    """创建角色和用户"""
    try:
        # 创建角色
        roles_data = [
            {
                'name': 'admin',
                'display_name': '管理员',
                'description': '系统管理员，拥有所有权限，可以管理用户、角色和系统配置',
                'permissions': ROLE_PERMISSIONS['admin']
            },
            {
                'name': 'analyst',
                'display_name': '数据分析师',
                'description': '数据分析师，拥有高级分析权限，可以进行预测分析和数据对比',
                'permissions': ROLE_PERMISSIONS['analyst']
            },
            {
                'name': 'user',
                'display_name': '普通用户',
                'description': '普通用户，只能查看基础分析功能',
                'permissions': ROLE_PERMISSIONS['user']
            }
        ]
        
        for role_data in roles_data:
            role, created = Role.objects.get_or_create(
                name=role_data['name'],
                defaults={
                    'display_name': role_data['display_name'],
                    'description': role_data['description'],
                    'permissions': role_data['permissions']
                }
            )
            
            if created:
                print(f"✅ 创建角色: {role.display_name}")
            else:
                # 更新权限配置
                role.permissions = role_data['permissions']
                role.save()
                print(f"🔄 更新角色: {role.display_name}")
        
        # 创建用户
        users_data = [
            {
                'username': 'admin',
                'password': 'admin123',
                'email': '<EMAIL>',
                'first_name': '系统',
                'last_name': '管理员',
                'role_name': 'admin',
                'is_superuser': True,
                'is_staff': True
            },
            {
                'username': 'analyst',
                'password': 'analyst123',
                'email': '<EMAIL>',
                'first_name': '数据',
                'last_name': '分析师',
                'role_name': 'analyst'
            },
            {
                'username': 'user',
                'password': 'user123',
                'email': '<EMAIL>',
                'first_name': '普通',
                'last_name': '用户',
                'role_name': 'user'
            }
        ]
        
        for user_data in users_data:
            if not CustomUser.objects.filter(username=user_data['username']).exists():
                role = Role.objects.get(name=user_data['role_name'])
                
                user = CustomUser.objects.create_user(
                    username=user_data['username'],
                    password=user_data['password'],
                    email=user_data['email'],
                    first_name=user_data['first_name'],
                    last_name=user_data['last_name'],
                    role=role,
                    is_superuser=user_data.get('is_superuser', False),
                    is_staff=user_data.get('is_staff', False)
                )
                
                print(f"✅ 创建用户: {user.username} ({role.display_name})")
            else:
                print(f"⚠️  用户已存在: {user_data['username']}")
        
        return True
        
    except Exception as e:
        print(f"❌ 角色和用户创建失败: {e}")
        return False

def main():
    """主函数"""
    print("🚀 开始设置数据库和权限系统...")
    
    # 1. 创建数据库表
    print("\n📋 创建数据库表...")
    if not execute_sql_file():
        print("❌ 数据库表创建失败，退出")
        sys.exit(1)
    
    # 2. 创建角色和用户
    print("\n👥 创建角色和用户...")
    if not create_roles_and_users():
        print("❌ 角色和用户创建失败，退出")
        sys.exit(1)
    
    print("\n✅ 数据库和权限系统设置完成！")
    print("\n📝 默认用户账号:")
    print("   管理员: admin / admin123")
    print("   分析师: analyst / analyst123")
    print("   普通用户: user / user123")
    print("\n🔗 现在可以访问 http://localhost:8000/login/ 进行登录")

if __name__ == '__main__':
    main()
