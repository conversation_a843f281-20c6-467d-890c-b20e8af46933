#!/usr/bin/env python
"""
创建测试用户的脚本 - 不依赖数据库表结构
"""
import os
import sys
import django

# 添加项目路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# 设置Django环境
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'phone_analysis.settings')
django.setup()

from django.contrib.auth.models import User
from django.contrib.auth import get_user_model

def create_simple_users():
    """创建简单的测试用户"""
    try:
        # 获取用户模型
        User = get_user_model()
        
        # 创建测试用户
        users_data = [
            {
                'username': 'admin',
                'password': 'admin123',
                'email': '<EMAIL>',
                'first_name': '系统',
                'last_name': '管理员',
                'is_superuser': True,
                'is_staff': True
            },
            {
                'username': 'analyst',
                'password': 'analyst123',
                'email': '<EMAIL>',
                'first_name': '数据',
                'last_name': '分析师',
                'is_superuser': False,
                'is_staff': False
            },
            {
                'username': 'user',
                'password': 'user123',
                'email': '<EMAIL>',
                'first_name': '普通',
                'last_name': '用户',
                'is_superuser': False,
                'is_staff': False
            }
        ]
        
        for user_data in users_data:
            # 检查用户是否已存在
            if User.objects.filter(username=user_data['username']).exists():
                print(f"⚠️  用户 {user_data['username']} 已存在，跳过创建")
                continue
            
            # 创建用户
            user = User.objects.create_user(
                username=user_data['username'],
                password=user_data['password'],
                email=user_data['email'],
                first_name=user_data['first_name'],
                last_name=user_data['last_name']
            )
            
            user.is_superuser = user_data['is_superuser']
            user.is_staff = user_data['is_staff']
            user.save()
            
            print(f"✅ 创建用户: {user.username}")
        
        print("\n✅ 用户创建完成！")
        print("\n📝 测试账号:")
        print("   管理员: admin / admin123")
        print("   分析师: analyst / analyst123")
        print("   普通用户: user / user123")
        
        return True
        
    except Exception as e:
        print(f"❌ 用户创建失败: {e}")
        return False

if __name__ == '__main__':
    print("🚀 开始创建测试用户...")
    create_simple_users()
