{% extends "base.html" %}

{% block title %}同价位机型对比 - 手机数据分析平台{% endblock %}

{% block extra_css %}
<style>
        .comparison-container {
            width: 100%;
            padding: 20px;
        }
        
        .comparison-section {
            background: #fff;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .section-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 2px solid #f0f0f0;
        }
        
        .section-title {
            font-size: 24px;
            font-weight: bold;
            color: #333;
        }
        
        .price-range-selector {
            display: flex;
            gap: 10px;
            margin-bottom: 20px;
            flex-wrap: wrap;
        }
        
        .range-button {
            padding: 10px 20px;
            border: 2px solid #ddd;
            background: white;
            cursor: pointer;
            border-radius: 25px;
            transition: all 0.3s;
            font-weight: 500;
        }
        
        .range-button.active {
            background: #007bff;
            color: white;
            border-color: #007bff;
        }
        
        .range-button:hover {
            border-color: #007bff;
            transform: translateY(-2px);
        }
        
        .range-stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-bottom: 20px;
        }
        
        .stat-card {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 8px;
            text-align: center;
            border-left: 4px solid #007bff;
        }
        
        .stat-value {
            font-size: 24px;
            font-weight: bold;
            color: #007bff;
        }
        
        .stat-label {
            font-size: 14px;
            color: #666;
            margin-top: 5px;
        }
        
        .chart-container {
            height: 400px;
            margin-bottom: 20px;
        }
        
        .phone-comparison-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
        }
        
        .phone-comparison-table th,
        .phone-comparison-table td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #eee;
        }
        
        .phone-comparison-table th {
            background-color: #f8f9fa;
            font-weight: bold;
            position: sticky;
            top: 0;
        }
        
        .phone-comparison-table tr:hover {
            background-color: #f8f9fa;
        }
        
        .star-rating {
            color: #ffc107;
            font-size: 16px;
        }
        
        .performance-score {
            display: inline-block;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: bold;
            color: white;
        }
        
        .score-excellent {
            background-color: #28a745;
        }
        
        .score-good {
            background-color: #17a2b8;
        }
        
        .score-average {
            background-color: #ffc107;
            color: #333;
        }
        
        .score-poor {
            background-color: #dc3545;
        }
        
        .similar-phones-section {
            margin-top: 20px;
        }
        
        .phone-selector {
            margin-bottom: 20px;
        }
        
        .phone-selector select {
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 16px;
            min-width: 300px;
        }
        
        .similar-phone-card {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 15px;
            border: 1px solid #eee;
            border-radius: 8px;
            margin-bottom: 10px;
            transition: all 0.3s;
        }
        
        .similar-phone-card:hover {
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
            transform: translateY(-2px);
        }
        
        .phone-info {
            flex: 1;
        }
        
        .phone-name {
            font-weight: bold;
            font-size: 16px;
            color: #333;
        }
        
        .phone-specs {
            font-size: 14px;
            color: #666;
            margin-top: 5px;
        }
        
        .similarity-score {
            background: #007bff;
            color: white;
            padding: 8px 12px;
            border-radius: 20px;
            font-weight: bold;
        }
        
        .loading {
            text-align: center;
            padding: 40px;
            color: #666;
        }
        
        .error {
            text-align: center;
            padding: 40px;
            color: #dc3545;
            background: #f8d7da;
            border-radius: 6px;
        }
        
        .tabs {
            display: flex;
            gap: 10px;
            margin-bottom: 20px;
        }
        
        .tab-button {
            padding: 10px 20px;
            border: 1px solid #ddd;
            background: white;
            cursor: pointer;
            border-radius: 4px;
            transition: all 0.3s;
        }
        
        .tab-button.active {
            background: #007bff;
            color: white;
            border-color: #007bff;
        }
        
        .tab-content {
            display: none;
        }
        
        .tab-content.active {
            display: block;
        }

        .pagination-container {
            margin-top: 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            flex-wrap: wrap;
            gap: 10px;
        }

        .pagination {
            display: flex;
            gap: 5px;
        }

        .pagination button {
            padding: 8px 12px;
            border: 1px solid #ddd;
            background: white;
            cursor: pointer;
            border-radius: 4px;
            transition: all 0.3s;
        }

        .pagination button:hover {
            background-color: #f8f9fa;
            border-color: #007bff;
        }

        .pagination button.active {
            background-color: #007bff;
            color: white;
            border-color: #007bff;
        }

        .pagination button:disabled {
            background-color: #f8f9fa;
            color: #6c757d;
            cursor: not-allowed;
            border-color: #ddd;
        }

        .pagination-info {
            font-size: 14px;
            color: #666;
        }

        .table-info {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
            padding: 10px;
            background: #f8f9fa;
            border-radius: 4px;
            font-size: 14px;
            color: #666;
        }

        .no-results {
            text-align: center;
            padding: 40px;
            color: #999;
            background: #f8f9fa;
            border-radius: 8px;
        }

        .visualization-section {
            margin: 20px 0;
            background: #fff;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .chart-tabs {
            display: flex;
            gap: 10px;
            margin-bottom: 20px;
            border-bottom: 1px solid #eee;
            padding-bottom: 10px;
        }

        .chart-tab-button {
            padding: 8px 16px;
            border: 1px solid #ddd;
            background: white;
            cursor: pointer;
            border-radius: 4px 4px 0 0;
            transition: all 0.3s;
            font-size: 14px;
        }

        .chart-tab-button.active {
            background: #007bff;
            color: white;
            border-color: #007bff;
            border-bottom-color: transparent;
        }

        .chart-tab-button:hover {
            background: #f8f9fa;
            border-color: #007bff;
        }

        .chart-tab-button.active:hover {
            background: #0056b3;
        }

        .chart-content {
            display: none;
        }

        .chart-content.active {
            display: block;
        }

        .chart-container {
            height: 450px;
            margin-bottom: 20px;
            border: 1px solid #eee;
            border-radius: 6px;
            background: #fafafa;
        }

        .top-phones-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }

        .phone-card {
            background: #fff;
            border: 1px solid #eee;
            border-radius: 8px;
            padding: 15px;
            transition: all 0.3s;
            position: relative;
        }

        .phone-card:hover {
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
            transform: translateY(-2px);
        }

        .phone-card.top-1 {
            border-left: 4px solid #ffd700;
        }

        .phone-card.top-2 {
            border-left: 4px solid #c0c0c0;
        }

        .phone-card.top-3 {
            border-left: 4px solid #cd7f32;
        }

        .phone-rank {
            position: absolute;
            top: -10px;
            right: -10px;
            background: #007bff;
            color: white;
            border-radius: 50%;
            width: 30px;
            height: 30px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            font-size: 14px;
        }

        .phone-title {
            font-size: 18px;
            font-weight: bold;
            color: #333;
            margin-bottom: 10px;
        }

        .phone-specs-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 8px;
            margin: 15px 0;
        }

        .spec-item {
            display: flex;
            justify-content: space-between;
            font-size: 14px;
        }

        .spec-label {
            color: #666;
        }

        .spec-value {
            font-weight: bold;
            color: #333;
        }

        .phone-score {
            text-align: center;
            margin-top: 15px;
            padding-top: 15px;
            border-top: 1px solid #eee;
        }

        .score-circle {
            display: inline-block;
            width: 60px;
            height: 60px;
            border-radius: 50%;
            background: conic-gradient(#007bff 0deg, #007bff calc(var(--score) * 3.6deg), #eee calc(var(--score) * 3.6deg), #eee 360deg);
            display: flex;
            align-items: center;
            justify-content: center;
            color: #333;
            font-weight: bold;
            font-size: 14px;
            position: relative;
        }

        .score-circle::before {
            content: '';
            position: absolute;
            width: 45px;
            height: 45px;
            background: white;
            border-radius: 50%;
            z-index: 1;
        }

        .score-text {
            position: relative;
            z-index: 2;
        }
</style>
{% endblock %}

{% block content %}
<div class="comparison-container">
                    <!-- 功能选项卡 -->
                    <div class="comparison-section">
                        <div class="section-header">
                            <h2 class="section-title">同价位机型对比分析</h2>
                        </div>
                        
                        <div class="tabs">
                            <button class="tab-button active" onclick="showTab('price-range')">价格区间分析</button>
                            <button class="tab-button" onclick="showTab('similar-phones')">相似机型推荐</button>
                        </div>
                        
                        <!-- 价格区间分析标签页 -->
                        <div id="price-range-tab" class="tab-content active">
                            <div class="price-range-selector" id="priceRangeSelector">
                                <!-- 动态生成价格区间按钮 -->
                            </div>

                            <div id="rangeStatsContainer" class="range-stats" style="display: none;">
                                <!-- 动态生成统计信息 -->
                            </div>

                            <!-- 可视化图表区域 -->
                            <div class="visualization-section">
                                <div class="chart-tabs">
                                    <button class="chart-tab-button active" onclick="showChart('parallel')">平行坐标图</button>
                                    <button class="chart-tab-button" onclick="showChart('radar')">雷达图对比</button>
                                    <button class="chart-tab-button" onclick="showChart('scatter')">散点图分析</button>
                                    <button class="chart-tab-button" onclick="showChart('bar')">参数对比图</button>
                                </div>

                                <div id="parallelChart" class="chart-container chart-content active"></div>
                                <div id="radarChart" class="chart-container chart-content"></div>
                                <div id="scatterChart" class="chart-container chart-content"></div>
                                <div id="barChart" class="chart-container chart-content"></div>
                            </div>

                            <div id="comparisonTableContainer">
                                <!-- 动态生成对比表格 -->
                            </div>
                        </div>
                        
                        <!-- 相似机型推荐标签页 -->
                        <div id="similar-phones-tab" class="tab-content">
                            <div class="phone-selector">
                                <label for="phoneSelect">选择手机型号：</label>
                                <select id="phoneSelect" onchange="findSimilarPhones()">
                                    <option value="">请选择手机型号</option>
                                    <!-- 动态生成手机选项 -->
                                </select>
                            </div>
                            
                            <div id="similarPhonesContainer">
                                <!-- 动态生成相似手机列表 -->
                            </div>
                        </div>
                        
                        <div id="loadingIndicator" class="loading">正在加载数据...</div>
                        <div id="errorIndicator" class="error" style="display: none;"></div>
                    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
        let priceRangeData = null;
        let currentRange = null;
        let currentPage = 1;
        let pageSize = 10;

        // 页面加载完成后初始化
        $(document).ready(function() {
            loadPriceRangeData();
            loadPhoneOptions();
        });

        // 加载价格区间数据
        function loadPriceRangeData() {
            $.get('/api/price-range-analysis/')
                .done(function(data) {
                    if (data.success) {
                        priceRangeData = data.price_ranges;
                        displayPriceRangeSelector();
                        $('#loadingIndicator').hide();
                    } else {
                        showError(data.error);
                    }
                })
                .fail(function() {
                    showError('加载价格区间数据失败');
                });
        }

        // 显示价格区间选择器
        function displayPriceRangeSelector() {
            const selector = $('#priceRangeSelector');
            selector.empty();

            priceRangeData.forEach((range, index) => {
                const button = $(`
                    <button class="range-button" onclick="selectPriceRange(${index})">
                        ${range.range_name}
                        <br><small>${range.phone_count}款手机</small>
                    </button>
                `);
                selector.append(button);
            });

            // 默认选择第一个区间
            if (priceRangeData.length > 0) {
                selectPriceRange(0);
            }
        }

        // 图表切换功能
        function showChart(chartType) {
            // 切换标签页
            $('.chart-tab-button').removeClass('active');
            $(`.chart-tab-button:contains('${getChartName(chartType)}')`).addClass('active');

            // 切换图表内容
            $('.chart-content').removeClass('active');
            $(`#${chartType}Chart`).addClass('active');

            // 如果有当前选中的价格区间，重新渲染对应图表
            if (currentRange) {
                switch(chartType) {
                    case 'parallel':
                        displayParallelChart(currentRange);
                        break;
                    case 'radar':
                        displayRadarChart(currentRange);
                        break;
                    case 'scatter':
                        displayScatterChart(currentRange);
                        break;
                    case 'bar':
                        displayBarChart(currentRange);
                        break;
                }
            }
        }

        function getChartName(chartType) {
            const names = {
                'parallel': '平行坐标图',
                'radar': '雷达图对比',
                'scatter': '散点图分析',
                'bar': '参数对比图'
            };
            return names[chartType] || chartType;
        }

        // 选择价格区间
        function selectPriceRange(index) {
            $('.range-button').removeClass('active');
            $('.range-button').eq(index).addClass('active');

            currentRange = priceRangeData[index];
            currentPage = 1; // 重置页码
            displayRangeStats(currentRange);
            displayTopPhonesCards(currentRange);

            // 显示当前激活的图表
            const activeChart = $('.chart-tab-button.active').text();
            if (activeChart.includes('平行坐标图')) {
                displayParallelChart(currentRange);
            } else if (activeChart.includes('雷达图对比')) {
                displayRadarChart(currentRange);
            } else if (activeChart.includes('散点图分析')) {
                displayScatterChart(currentRange);
            } else if (activeChart.includes('参数对比图')) {
                displayBarChart(currentRange);
            }

            loadPaginatedData(currentRange.range_name, 1);
        }

        // 显示顶级手机卡片
        function displayTopPhonesCards(range) {
            const topPhones = range.phones.slice(0, 3);

            let cardsHtml = `
                <div class="top-phones-section">
                    <h3>🏆 ${range.range_name} 推荐机型 TOP3</h3>
                    <div class="top-phones-grid">
            `;

            topPhones.forEach((phone, index) => {
                const rankClass = `top-${index + 1}`;
                const totalScore = phone.total_score || 0;

                cardsHtml += `
                    <div class="phone-card ${rankClass}">
                        <div class="phone-rank">${index + 1}</div>
                        <div class="phone-title">${phone.brand} ${phone.model}</div>

                        <div class="phone-specs-grid">
                            <div class="spec-item">
                                <span class="spec-label">💰 价格</span>
                                <span class="spec-value">¥${phone.price.toFixed(0)}</span>
                            </div>
                            <div class="spec-item">
                                <span class="spec-label">⭐ 评分</span>
                                <span class="spec-value">${phone.rating.toFixed(1)}/10</span>
                            </div>
                            <div class="spec-item">
                                <span class="spec-label">💾 内存</span>
                                <span class="spec-value">${phone.memory}GB</span>
                            </div>
                            <div class="spec-item">
                                <span class="spec-label">🔋 电池</span>
                                <span class="spec-value">${phone.battery}mAh</span>
                            </div>
                            <div class="spec-item">
                                <span class="spec-label">📷 摄像头</span>
                                <span class="spec-value">${phone.camera}MP</span>
                            </div>
                            <div class="spec-item">
                                <span class="spec-label">🏷️ 品牌</span>
                                <span class="spec-value">${phone.brand}</span>
                            </div>
                        </div>

                        <div class="phone-score">
                            <div class="score-circle" style="--score: ${totalScore}">
                                <div class="score-text">${totalScore.toFixed(0)}</div>
                            </div>
                            <div style="margin-top: 8px; font-size: 12px; color: #666;">综合评分</div>
                        </div>
                    </div>
                `;
            });

            cardsHtml += `
                    </div>
                </div>
            `;

            // 在统计信息后插入卡片
            $('#rangeStatsContainer').after(cardsHtml);

            // 移除之前的卡片（如果存在）
            $('.top-phones-section').not(':last').remove();
        }

        // 显示区间统计信息
        function displayRangeStats(range) {
            const container = $('#rangeStatsContainer');
            container.html(`
                <div class="stat-card">
                    <div class="stat-value">${range.phone_count}</div>
                    <div class="stat-label">手机数量</div>
                </div>
                <div class="stat-card">
                    <div class="stat-value">¥${range.avg_price.toFixed(0)}</div>
                    <div class="stat-label">平均价格</div>
                </div>
                <div class="stat-card">
                    <div class="stat-value">${range.avg_rating.toFixed(1)}</div>
                    <div class="stat-label">平均评分</div>
                </div>
                <div class="stat-card">
                    <div class="stat-value">${range.avg_memory.toFixed(0)}GB</div>
                    <div class="stat-label">平均内存</div>
                </div>
                <div class="stat-card">
                    <div class="stat-value">${range.avg_battery.toFixed(0)}mAh</div>
                    <div class="stat-label">平均电池</div>
                </div>
                <div class="stat-card">
                    <div class="stat-value">${range.avg_camera.toFixed(0)}MP</div>
                    <div class="stat-label">平均摄像头</div>
                </div>
            `);
            container.show();
        }

        // 显示平行坐标图
        function displayParallelChart(range) {
            const chart = echarts.init(document.getElementById('parallelChart'));

            // 准备数据
            const phones = range.phones.slice(0, 20); // 限制显示数量以提高性能
            const data = phones.map(phone => [
                phone.price,
                phone.rating * 10, // 10星制评分放大显示
                phone.memory,
                phone.battery / 100, // 缩小电池容量以便显示
                phone.camera,
                phone.brand + ' ' + phone.model
            ]);

            const option = {
                title: {
                    text: `${range.range_name} 手机参数对比 - 平行坐标图`,
                    left: 'center',
                    textStyle: {
                        fontSize: 16,
                        fontWeight: 'bold'
                    }
                },
                tooltip: {
                    trigger: 'item',
                    formatter: function(params) {
                        const phone = phones[params.dataIndex];
                        return `
                            <div style="padding: 10px;">
                                <strong style="font-size: 14px;">${phone.brand} ${phone.model}</strong><br/>
                                <div style="margin: 8px 0;">
                                    💰 价格: ¥${phone.price}<br/>
                                    ⭐ 评分: ${phone.rating.toFixed(1)}/10<br/>
                                    💾 内存: ${phone.memory}GB<br/>
                                    🔋 电池: ${phone.battery}mAh<br/>
                                    📷 摄像头: ${phone.camera}MP
                                </div>
                            </div>
                        `;
                    }
                },
                parallelAxis: [
                    {dim: 0, name: '价格(元)', min: range.min_price, max: range.max_price, nameTextStyle: {fontWeight: 'bold'}},
                    {dim: 1, name: '评分(×10)', min: 0, max: 100, nameTextStyle: {fontWeight: 'bold'}},
                    {dim: 2, name: '内存(GB)', min: 0, nameTextStyle: {fontWeight: 'bold'}},
                    {dim: 3, name: '电池(×100mAh)', min: 0, nameTextStyle: {fontWeight: 'bold'}},
                    {dim: 4, name: '摄像头(MP)', min: 0, nameTextStyle: {fontWeight: 'bold'}}
                ],
                series: {
                    type: 'parallel',
                    lineStyle: {
                        width: 3,
                        opacity: 0.8
                    },
                    data: data,
                    emphasis: {
                        lineStyle: {
                            width: 4,
                            opacity: 1
                        }
                    }
                }
            };

            chart.setOption(option);

            window.addEventListener('resize', function() {
                chart.resize();
            });
        }

        // 显示雷达图对比
        function displayRadarChart(range) {
            const chart = echarts.init(document.getElementById('radarChart'));

            // 取前5名手机进行对比
            const topPhones = range.phones.slice(0, 5);

            // 计算各参数的最大值用于标准化
            const maxPrice = Math.max(...range.phones.map(p => p.price));
            const maxMemory = Math.max(...range.phones.map(p => p.memory));
            const maxBattery = Math.max(...range.phones.map(p => p.battery));
            const maxCamera = Math.max(...range.phones.map(p => p.camera));

            const seriesData = topPhones.map((phone, index) => ({
                name: `${phone.brand} ${phone.model}`,
                value: [
                    (phone.rating / 10 * 100).toFixed(1), // 评分标准化到100
                    (phone.price / maxPrice * 100).toFixed(1), // 价格标准化（越低越好，所以用100-标准化值）
                    (phone.memory / maxMemory * 100).toFixed(1), // 内存标准化
                    (phone.battery / maxBattery * 100).toFixed(1), // 电池标准化
                    (phone.camera / maxCamera * 100).toFixed(1) // 摄像头标准化
                ],
                itemStyle: {
                    color: ['#ff6b6b', '#4ecdc4', '#45b7d1', '#96ceb4', '#feca57'][index]
                }
            }));

            const option = {
                title: {
                    text: `${range.range_name} TOP5 手机雷达图对比`,
                    left: 'center',
                    textStyle: {
                        fontSize: 16,
                        fontWeight: 'bold'
                    }
                },
                tooltip: {
                    trigger: 'item',
                    formatter: function(params) {
                        const phone = topPhones[params.dataIndex];
                        return `
                            <div style="padding: 10px;">
                                <strong>${params.name}</strong><br/>
                                💰 价格: ¥${phone.price}<br/>
                                ⭐ 评分: ${phone.rating.toFixed(1)}/10<br/>
                                💾 内存: ${phone.memory}GB<br/>
                                🔋 电池: ${phone.battery}mAh<br/>
                                📷 摄像头: ${phone.camera}MP
                            </div>
                        `;
                    }
                },
                legend: {
                    data: topPhones.map(phone => `${phone.brand} ${phone.model}`),
                    top: 30,
                    type: 'scroll'
                },
                radar: {
                    indicator: [
                        { name: '评分', max: 100 },
                        { name: '性价比', max: 100 },
                        { name: '内存', max: 100 },
                        { name: '电池', max: 100 },
                        { name: '摄像头', max: 100 }
                    ],
                    center: ['50%', '60%'],
                    radius: '60%'
                },
                series: [{
                    type: 'radar',
                    data: seriesData
                }]
            };

            chart.setOption(option);

            window.addEventListener('resize', function() {
                chart.resize();
            });
        }

        // 显示散点图分析
        function displayScatterChart(range) {
            const chart = echarts.init(document.getElementById('scatterChart'));

            const data = range.phones.map(phone => [
                phone.price,
                phone.rating,
                phone.memory,
                `${phone.brand} ${phone.model}`
            ]);

            const option = {
                title: {
                    text: `${range.range_name} 价格vs评分散点图`,
                    left: 'center',
                    textStyle: {
                        fontSize: 16,
                        fontWeight: 'bold'
                    }
                },
                tooltip: {
                    trigger: 'item',
                    formatter: function(params) {
                        return `
                            <div style="padding: 10px;">
                                <strong>${params.data[3]}</strong><br/>
                                💰 价格: ¥${params.data[0]}<br/>
                                ⭐ 评分: ${params.data[1].toFixed(1)}/10<br/>
                                💾 内存: ${params.data[2]}GB
                            </div>
                        `;
                    }
                },
                xAxis: {
                    type: 'value',
                    name: '价格(元)',
                    nameLocation: 'middle',
                    nameGap: 30,
                    axisLabel: {
                        formatter: '¥{value}'
                    }
                },
                yAxis: {
                    type: 'value',
                    name: '评分',
                    nameLocation: 'middle',
                    nameGap: 40,
                    min: 0,
                    max: 10,
                    axisLabel: {
                        formatter: '{value}/10'
                    }
                },
                series: [{
                    type: 'scatter',
                    data: data,
                    symbolSize: function(data) {
                        return Math.sqrt(data[2]) * 3; // 根据内存大小调整点的大小
                    },
                    itemStyle: {
                        color: function(params) {
                            // 根据评分设置颜色
                            const rating = params.data[1];
                            if (rating >= 9) return '#ff6b6b';
                            if (rating >= 8) return '#4ecdc4';
                            if (rating >= 7) return '#45b7d1';
                            if (rating >= 6) return '#96ceb4';
                            return '#feca57';
                        },
                        opacity: 0.8
                    },
                    emphasis: {
                        itemStyle: {
                            opacity: 1,
                            borderColor: '#333',
                            borderWidth: 2
                        }
                    }
                }]
            };

            chart.setOption(option);

            window.addEventListener('resize', function() {
                chart.resize();
            });
        }

        // 显示参数对比柱状图
        function displayBarChart(range) {
            const chart = echarts.init(document.getElementById('barChart'));

            // 取前8名手机
            const topPhones = range.phones.slice(0, 8);
            const phoneNames = topPhones.map(phone => `${phone.brand}\n${phone.model}`);

            const option = {
                title: {
                    text: `${range.range_name} TOP8 手机参数对比`,
                    left: 'center',
                    textStyle: {
                        fontSize: 16,
                        fontWeight: 'bold'
                    }
                },
                tooltip: {
                    trigger: 'axis',
                    axisPointer: {
                        type: 'shadow'
                    }
                },
                legend: {
                    data: ['价格(千元)', '评分', '内存(GB)', '电池(千mAh)', '摄像头(10MP)'],
                    top: 30
                },
                xAxis: {
                    type: 'category',
                    data: phoneNames,
                    axisLabel: {
                        interval: 0,
                        rotate: 30,
                        fontSize: 10
                    }
                },
                yAxis: {
                    type: 'value',
                    name: '标准化数值'
                },
                series: [
                    {
                        name: '价格(千元)',
                        type: 'bar',
                        data: topPhones.map(phone => (phone.price / 1000).toFixed(1)),
                        itemStyle: { color: '#ff6b6b' }
                    },
                    {
                        name: '评分',
                        type: 'bar',
                        data: topPhones.map(phone => phone.rating.toFixed(1)),
                        itemStyle: { color: '#4ecdc4' }
                    },
                    {
                        name: '内存(GB)',
                        type: 'bar',
                        data: topPhones.map(phone => phone.memory),
                        itemStyle: { color: '#45b7d1' }
                    },
                    {
                        name: '电池(千mAh)',
                        type: 'bar',
                        data: topPhones.map(phone => (phone.battery / 1000).toFixed(1)),
                        itemStyle: { color: '#96ceb4' }
                    },
                    {
                        name: '摄像头(10MP)',
                        type: 'bar',
                        data: topPhones.map(phone => (phone.camera / 10).toFixed(1)),
                        itemStyle: { color: '#feca57' }
                    }
                ]
            };

            chart.setOption(option);

            window.addEventListener('resize', function() {
                chart.resize();
            });
        }

        // 加载分页数据
        function loadPaginatedData(rangeName, page) {
            $('#comparisonTableContainer').html('<div class="loading">正在加载数据...</div>');

            $.get('/api/price-range-analysis/', {
                range_name: rangeName,
                page: page,
                page_size: pageSize
            })
            .done(function(data) {
                if (data.success && data.price_ranges.length > 0) {
                    const rangeData = data.price_ranges[0];
                    displayPaginatedTable(rangeData);
                } else {
                    $('#comparisonTableContainer').html('<div class="error">加载失败</div>');
                }
            })
            .fail(function() {
                $('#comparisonTableContainer').html('<div class="error">加载失败</div>');
            });
        }

        // 显示分页表格
        function displayPaginatedTable(range) {
            const container = $('#comparisonTableContainer');

            if (!range.phones || range.phones.length === 0) {
                container.html('<div class="no-results">该价格区间暂无手机数据</div>');
                return;
            }

            let tableHtml = `
                <h3>机型详细对比 (按综合评分排序) - ${range.range_name}</h3>
                <div class="table-info">
                    <span>共 ${range.pagination ? range.pagination.total_phones : range.phones.length} 款手机</span>
                    ${range.pagination ? `<span>第 ${range.pagination.current_page} 页，共 ${range.pagination.total_pages} 页</span>` : ''}
                </div>
                <table class="phone-comparison-table">
                    <thead>
                        <tr>
                            <th>排名</th>
                            <th>品牌型号</th>
                            <th>价格</th>
                            <th>评分</th>
                            <th>内存</th>
                            <th>电池</th>
                            <th>摄像头</th>
                            <th>综合评分</th>
                        </tr>
                    </thead>
                    <tbody>
            `;

            range.phones.forEach((phone, index) => {
                const globalRank = range.pagination ?
                    (range.pagination.current_page - 1) * range.pagination.page_size + index + 1 :
                    index + 1;

                // 10星制星级显示
                const fullStars = Math.floor(phone.rating);
                const halfStar = phone.rating - fullStars >= 0.5 ? 1 : 0;
                const emptyStars = Math.max(0, 10 - fullStars - halfStar);
                const stars = '★'.repeat(fullStars) + (halfStar ? '☆' : '') + '☆'.repeat(Math.min(emptyStars, 10));

                const totalScore = phone.total_score || 0;
                const scoreClass = totalScore >= 80 ? 'score-excellent' :
                                 totalScore >= 70 ? 'score-good' :
                                 totalScore >= 60 ? 'score-average' : 'score-poor';

                tableHtml += `
                    <tr>
                        <td>${globalRank}</td>
                        <td><strong>${phone.brand}</strong><br/>${phone.model}</td>
                        <td>¥${phone.price.toFixed(0)}</td>
                        <td>
                            <span class="star-rating">${stars}</span><br/>
                            ${phone.rating.toFixed(1)}
                        </td>
                        <td>${phone.memory}GB</td>
                        <td>${phone.battery}mAh</td>
                        <td>${phone.camera}MP</td>
                        <td>
                            <span class="performance-score ${scoreClass}">
                                ${totalScore.toFixed(0)}分
                            </span>
                        </td>
                    </tr>
                `;
            });

            tableHtml += '</tbody></table>';

            // 添加分页控件
            if (range.pagination && range.pagination.total_pages > 1) {
                tableHtml += '<div class="pagination-container">';
                tableHtml += '<div class="pagination">';

                // 上一页
                if (range.pagination.has_previous) {
                    tableHtml += `<button onclick="changePage(${range.pagination.current_page - 1})">上一页</button>`;
                }

                // 页码
                const startPage = Math.max(1, range.pagination.current_page - 2);
                const endPage = Math.min(range.pagination.total_pages, range.pagination.current_page + 2);

                for (let i = startPage; i <= endPage; i++) {
                    const activeClass = i === range.pagination.current_page ? 'active' : '';
                    tableHtml += `<button class="${activeClass}" onclick="changePage(${i})">${i}</button>`;
                }

                // 下一页
                if (range.pagination.has_next) {
                    tableHtml += `<button onclick="changePage(${range.pagination.current_page + 1})">下一页</button>`;
                }

                tableHtml += '</div>';
                tableHtml += `<div class="pagination-info">每页显示 ${range.pagination.page_size} 条，共 ${range.pagination.total_phones} 条记录</div>`;
                tableHtml += '</div>';
            }

            container.html(tableHtml);
        }

        // 切换页码
        function changePage(page) {
            if (currentRange) {
                currentPage = page;
                loadPaginatedData(currentRange.range_name, page);
            }
        }

        // 显示对比表格（原始版本，用于预览）
        function displayComparisonTable(range) {
            const container = $('#comparisonTableContainer');

            if (!range.phones || range.phones.length === 0) {
                container.html('<div class="no-results">该价格区间暂无手机数据</div>');
                return;
            }

            let previewHtml = `
                <div class="table-info">
                    <span>该价格区间共有 ${range.phone_count} 款手机 (已过滤价格为0的手机)</span>
                    <span>点击下方按钮查看完整分页列表</span>
                </div>
                <div style="text-align: center; margin: 20px 0;">
                    <button class="btn btn-primary" onclick="loadPaginatedData(currentRange.range_name, 1)" style="padding: 12px 24px; font-size: 16px; background: #007bff; color: white; border: none; border-radius: 6px; cursor: pointer;">
                        查看完整列表 (每页10条，支持分页)
                    </button>
                </div>
            `;

            container.html(previewHtml);
        }

        // 加载手机选项
        function loadPhoneOptions() {
            $.get('/api/price-range-analysis/')
                .done(function(data) {
                    if (data.success) {
                        const select = $('#phoneSelect');
                        select.empty().append('<option value="">请选择手机型号</option>');

                        const allPhones = [];
                        data.price_ranges.forEach(range => {
                            range.phones.forEach(phone => {
                                allPhones.push(phone);
                            });
                        });

                        // 按品牌和型号排序
                        allPhones.sort((a, b) => {
                            if (a.brand !== b.brand) {
                                return a.brand.localeCompare(b.brand);
                            }
                            return a.model.localeCompare(b.model);
                        });

                        allPhones.forEach(phone => {
                            select.append(`
                                <option value="${phone.id}">
                                    ${phone.brand} ${phone.model} (¥${phone.price.toFixed(0)})
                                </option>
                            `);
                        });
                    }
                });
        }

        // 查找相似手机
        function findSimilarPhones() {
            const phoneId = $('#phoneSelect').val();
            if (!phoneId) {
                $('#similarPhonesContainer').empty();
                return;
            }

            $('#similarPhonesContainer').html('<div class="loading">正在查找相似手机...</div>');

            $.get('/api/similar-phones/', {phone_id: phoneId})
                .done(function(data) {
                    if (data.success) {
                        displaySimilarPhones(data);
                    } else {
                        $('#similarPhonesContainer').html(`<div class="error">查找失败: ${data.error}</div>`);
                    }
                })
                .fail(function() {
                    $('#similarPhonesContainer').html('<div class="error">查找相似手机失败</div>');
                });
        }

        // 显示相似手机
        function displaySimilarPhones(data) {
            const container = $('#similarPhonesContainer');

            let html = `
                <h3>与 ${data.target_phone.brand} ${data.target_phone.model} 相似的手机</h3>
                <div style="margin-bottom: 20px; padding: 15px; background: #e3f2fd; border-radius: 8px;">
                    <strong>目标手机:</strong> ${data.target_phone.brand} ${data.target_phone.model}
                    (¥${data.target_phone.price.toFixed(0)}, 评分: ${data.target_phone.rating.toFixed(1)})
                </div>
            `;

            if (data.similar_phones.length === 0) {
                html += '<div class="error">没有找到相似的手机</div>';
            } else {
                data.similar_phones.forEach((phone, index) => {
                    const similarityPercent = (phone.similarity_score * 100).toFixed(0);
                    html += `
                        <div class="similar-phone-card">
                            <div class="phone-info">
                                <div class="phone-name">${phone.brand} ${phone.model}</div>
                                <div class="phone-specs">
                                    价格: ¥${phone.price.toFixed(0)} |
                                    评分: ${phone.rating.toFixed(1)} |
                                    内存: ${phone.memory}GB |
                                    电池: ${phone.battery}mAh |
                                    摄像头: ${phone.camera}MP
                                </div>
                            </div>
                            <div class="similarity-score">${similarityPercent}% 相似</div>
                        </div>
                    `;
                });
            }

            container.html(html);
        }

        // 标签页切换
        function showTab(tabName) {
            $('.tab-button').removeClass('active');
            $('.tab-content').removeClass('active');

            $(`button[onclick="showTab('${tabName}')"]`).addClass('active');
            $(`#${tabName}-tab`).addClass('active');
        }

        // 显示错误信息
        function showError(error) {
            $('#loadingIndicator').hide();
            $('#errorIndicator').text('加载失败: ' + error).show();
        }
</script>
{% endblock %}
