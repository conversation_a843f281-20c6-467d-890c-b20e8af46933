<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}手机数据分析平台{% endblock %}</title>
    <link rel="stylesheet" href="/static/css/style123.css">
    <script src="https://cdn.jsdelivr.net/npm/echarts@5.4.3/dist/echarts.min.js"></script>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <style>
        .navbar {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 20px;
        }
        
        .user-info {
            display: flex;
            align-items: center;
            gap: 15px;
            color: white;
        }
        
        .user-welcome {
            font-size: 14px;
            color: #fff;
        }
        
        .logout-btn {
            background: rgba(255,255,255,0.2);
            color: white;
            padding: 8px 16px;
            border-radius: 4px;
            text-decoration: none;
            font-size: 14px;
            transition: background 0.3s;
        }
        
        .logout-btn:hover {
            background: rgba(255,255,255,0.3);
            text-decoration: none;
            color: white;
        }
        
        .permission-badge {
            display: inline-block;
            background: #007bff;
            color: white;
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 12px;
            margin-left: 8px;
        }
        
        .navbar-menu {
            display: flex;
            list-style: none;
            margin: 0;
            padding: 0;
            gap: 20px;
        }
        
        .navbar-menu li {
            position: relative;
        }
        
        .navbar-menu a {
            color: white;
            text-decoration: none;
            padding: 10px 15px;
            display: block;
            transition: background 0.3s;
        }
        
        .navbar-menu a:hover {
            background: rgba(255,255,255,0.1);
            border-radius: 4px;
        }
        
        .dropdown-content {
            display: none;
            position: absolute;
            background: white;
            min-width: 160px;
            box-shadow: 0px 8px 16px 0px rgba(0,0,0,0.2);
            z-index: 1;
            border-radius: 4px;
            top: 100%;
            left: 0;
        }
        
        .dropdown-content a {
            color: #333;
            padding: 12px 16px;
            text-decoration: none;
            display: block;
        }
        
        .dropdown-content a:hover {
            background-color: #f1f1f1;
        }
        
        .dropdown:hover .dropdown-content {
            display: block;
        }
    </style>
    {% block extra_css %}{% endblock %}
</head>
<body>
    <header>
        <nav class="navbar">
            <div class="navbar-brand">
                <a href="/" style="color: white; text-decoration: none; font-size: 18px; font-weight: bold;">
                    📱 手机数据分析平台
                </a>
            </div>
            <ul class="navbar-menu">
                <li><a href="/">首页</a></li>
                <li><a href="/brand-analysis">品牌分析</a></li>
                <li><a href="/price-distribution">价格分布</a></li>
                <li><a href="/phone-filter">手机筛选</a></li>
                <li><a href="/prediction-analysis">预测分析</a></li>
                <li><a href="/price-comparison">机型对比</a></li>
                <li class="dropdown">
                    <a href="#" class="dropbtn">更多分析</a>
                    <div class="dropdown-content">
                        <a href="/cpu-analysis">CPU 分析</a>
                        <a href="/memory-analysis">内存分析</a>
                        <a href="/users/">用户管理</a>
                        <a href="/logs/">登录日志</a>
                    </div>
                </li>
            </ul>
            {% if user.is_authenticated %}
            <div class="user-info">
                <span class="user-welcome">
                    {{ user.first_name }}{{ user.last_name }} 
                    <span class="permission-badge">{{ role }}</span>
                </span>
                <a href="/logout/" class="logout-btn">退出登录</a>
            </div>
            {% else %}
            <div class="user-info">
                <a href="/login/" class="logout-btn">登录</a>
            </div>
            {% endif %}
        </nav>
    </header>

    <div class="container">
        <div class="main-container">
            <nav class="sidebar">
                <ul class="mcd-menu">
                    <li>
                        <a href="/">
                            <i class="fa fa-home"></i>
                            <strong>首页</strong>
                            <small>返回主页</small>
                        </a>
                    </li>
                    <li>
                        <a href="/brand-analysis">
                            <i class="fa fa-edit"></i>
                            <strong>品牌分析</strong>
                            <small>分析品牌数据</small>
                        </a>
                    </li>
                    <li>
                        <a href="/price-distribution">
                            <i class="fa fa-gift"></i>
                            <strong>价格分析</strong>
                            <small>查看价格分布</small>
                        </a>
                    </li>
                    <li>
                        <a href="/phone-filter">
                            <i class="fa fa-filter"></i>
                            <strong>手机筛选</strong>
                            <small>自定义筛选条件</small>
                        </a>
                    </li>
                    <li>
                        <a href="/prediction-analysis">
                            <i class="fa fa-chart-line"></i>
                            <strong>预测分析</strong>
                            <small>趋势预测模型</small>
                        </a>
                    </li>
                    <li>
                        <a href="/price-comparison">
                            <i class="fa fa-balance-scale"></i>
                            <strong>机型对比</strong>
                            <small>同价位机型对比</small>
                        </a>
                    </li>
                    <li>
                        <a href="#">
                            <i class="fa fa-comments-o"></i>
                            <strong>更多分析</strong>
                            <small>其他分析选项</small>
                        </a>
                        <ul>
                            <li><a href="/cpu-analysis"><i class="fa fa-globe"></i>CPU 分析</a></li>
                            <li><a href="/memory-analysis"><i class="fa fa-group"></i>内存分析</a></li>
                            <li><a href="/users/"><i class="fa fa-users"></i>用户管理</a></li>
                            <li><a href="/logs/"><i class="fa fa-list"></i>登录日志</a></li>
                        </ul>
                    </li>
                </ul>
            </nav>
            <main class="content">
                {% block content %}
                {% endblock %}
            </main>
        </div>
    </div>

    {% block extra_js %}{% endblock %}
</body>
</html>
