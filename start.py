#!/usr/bin/env python
"""
项目启动脚本
"""
import os
import sys
import subprocess
import pymysql

def check_mysql_connection():
    """检查MySQL连接"""
    try:
        connection = pymysql.connect(
            host='localhost',
            user='root',
            password='root',
            database='phone_db',
            charset='utf8mb4'
        )
        connection.close()
        print("✅ MySQL连接正常")
        return True
    except Exception as e:
        print(f"❌ MySQL连接失败: {e}")
        print("请确保：")
        print("1. MySQL服务已启动")
        print("2. 数据库 'phone_db' 已创建")
        print("3. 用户名密码正确 (默认: root/root)")
        return False

def run_migrations():
    """运行数据库迁移"""
    try:
        print("🔄 运行数据库迁移...")
        subprocess.run([sys.executable, 'manage.py', 'makemigrations'], check=True)
        subprocess.run([sys.executable, 'manage.py', 'migrate'], check=True)
        print("✅ 数据库迁移完成")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ 数据库迁移失败: {e}")
        return False

def setup_initial_data():
    """设置初始数据"""
    try:
        print("🔄 设置初始用户和角色...")
        subprocess.run([sys.executable, 'scripts/setup_database.py'], check=True)
        print("✅ 初始数据设置完成")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ 初始数据设置失败: {e}")
        return False

def start_server():
    """启动开发服务器"""
    try:
        print("🚀 启动开发服务器...")
        print("访问地址: http://localhost:8000/login/")
        print("默认账号:")
        print("  管理员: admin / admin123")
        print("  分析师: analyst / analyst123")
        print("  普通用户: user / user123")
        print("\n按 Ctrl+C 停止服务器")
        subprocess.run([sys.executable, 'manage.py', 'runserver'], check=True)
    except KeyboardInterrupt:
        print("\n👋 服务器已停止")
    except subprocess.CalledProcessError as e:
        print(f"❌ 服务器启动失败: {e}")

def main():
    """主函数"""
    print("🎯 手机数据分析项目启动器")
    print("=" * 40)
    
    # 1. 检查MySQL连接
    if not check_mysql_connection():
        return
    
    # 2. 运行数据库迁移
    if not run_migrations():
        return
    
    # 3. 设置初始数据
    if not setup_initial_data():
        print("⚠️  初始数据设置失败，但可以继续运行")
    
    # 4. 启动服务器
    start_server()

if __name__ == '__main__':
    main()
