import pandas as pd
import pymysql
from sqlalchemy import create_engine
import re

def import_data():
    try:
        df = pd.read_csv('phone.csv', encoding='utf-8')

        # 处理评分字段（从"X.X星"格式中提取数值，保持原始数值）
        def extract_rating(rating_str):
            if pd.isna(rating_str) or rating_str == '无评价':
                return 8.0  # 默认评分改为8.0（10星制）
            try:
                # 提取星级数值，如"9.6星" -> 9.6，保持原始数值
                rating_match = re.search(r'(\d+(?:\.\d+)?)', str(rating_str))
                if rating_match:
                    rating_value = float(rating_match.group(1))
                    # 保持原始评分，不进行转换
                    return min(max(rating_value, 0), 10)  # 限制在0-10之间
                return 8.0
            except:
                return 8.0

        # 检查是否有评分列（可能是"好评率"或"rating"）
        rating_columns = ['好评率', 'rating', '评分']
        for col in rating_columns:
            if col in df.columns:
                print(f"处理评分列: {col}")
                df['rating'] = df[col].apply(extract_rating)
                if col != 'rating':
                    df = df.drop(columns=[col])  # 删除原始列
                break
        else:
            # 如果没有找到评分列，设置默认值
            df['rating'] = 4.0
            print("未找到评分列，使用默认评分4.0")

        # 显示处理后的评分统计
        print(f"评分统计: 最小值={df['rating'].min():.1f}, 最大值={df['rating'].max():.1f}, 平均值={df['rating'].mean():.1f}")

        # 重命名列为英文名称
        column_mapping = {
            '品牌': 'brand',
            '型号': 'model',
            '颜色': 'color',
            '价格': 'price',
            'CPU': 'cpu',
            '内存': 'memory',
            '电池容量': 'battery',
            '操作系统': 'os',
            '摄影': 'camera'
        }

        # 重命名存在的列
        for chinese_name, english_name in column_mapping.items():
            if chinese_name in df.columns:
                df = df.rename(columns={chinese_name: english_name})

        print(f"列名: {list(df.columns)}")

        engine = create_engine('mysql+pymysql://root:root@localhost/phone_db?charset=utf8mb4',
                              connect_args={'charset': 'utf8mb4'})

        # 直接替换表
        df.to_sql('phones', engine, if_exists='replace', index=False)
        print("数据导入成功！")
        print(f"共导入 {len(df)} 条记录")

    except Exception as e:
        print(f"导入数据时出错: {e}")

if __name__ == '__main__':
    import_data()