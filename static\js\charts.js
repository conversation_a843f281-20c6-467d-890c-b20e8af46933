$(document).ready(function() {
    const brandChart = echarts.init(document.getElementById('brandChart'));
    const priceChart = echarts.init(document.getElementById('priceChart'));

    // 获取品牌统计数据
    $.get('/api/brand-stats/', function(data) {
        const brands = data.brands;
        brandChart.setOption({
            title: {
                text: '手机品牌分布'
            },
            tooltip: {},
            xAxis: {
                data: brands.map(item => item.brand)
            },
            yAxis: {},
            series: [{
                name: '数量',
                type: 'bar',
                data: brands.map(item => item.count)
            }]
        });
    });

    // 获取价格分布数据
    $.get('/api/price-distribution/', function(data) {
        const distribution = data.distribution;
        priceChart.setOption({
            title: {
                text: '手机价格分布'
            },
            tooltip: {},
            xAxis: {
                type: 'category',
                data: Object.keys(distribution)
            },
            yAxis: {},
            series: [{
                name: '数量',
                type: 'bar',
                data: Object.values(distribution)
            }]
        });
    });
});