{% extends "base.html" %}

{% block title %}内存分析 - 手机数据分析平台{% endblock %}

{% block content %}
<h1>内存分析</h1>
<div id="memoryChart" class="chart"></div>
{% endblock %}

{% block extra_js %}
<script>
        var chartDom = document.getElementById('memoryChart');
        var myChart = echarts.init(chartDom);
        
        $.get('/api/memory-stats/', function(data) {
            var option = {
                title: {
                    text: '手机内存分布分析',
                    left: 'center'
                },
                tooltip: {
                    trigger: 'axis',
                    axisPointer: {
                        type: 'cross'
                    }
                },
                legend: {
                    data: ['手机数量', '平均价格'],
                    top: 30
                },
                grid: {
                    left: '3%',
                    right: '4%',
                    bottom: '15%',  
                    containLabel: true
                },
                dataZoom: [
                    {
                        type: 'slider', 
                        show: true,
                        xAxisIndex: [0],
                        start: 0,
                        end: 50,        
                        bottom: '2%'    
                    },
                    {
                        type: 'inside', 
                        xAxisIndex: [0],
                        start: 0,
                        end: 50,
                        zoomOnMouseWheel: true,  // 支持鼠标滚轮缩放
                        moveOnMouseMove: true     // 支持鼠标拖动平移
                    }
                ],
                xAxis: {
                    type: 'category',
                    data: data.memory_sizes,
                    name: '内存大小',
                    axisLabel: {
                        interval: 0,
                        rotate: 30
                    }
                },
                yAxis: [
                    {
                        type: 'value',
                        name: '手机数量',
                        position: 'left'
                    },
                    {
                        type: 'value',
                        name: '平均价格(元)',
                        position: 'right'
                    }
                ],
                series: [
                    {
                        name: '手机数量',
                        type: 'bar',
                        data: data.counts,
                        itemStyle: {
                            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                                {offset: 0, color: '#83bff6'},
                                {offset: 0.5, color: '#188df0'},
                                {offset: 1, color: '#188df0'}
                            ])
                        }
                    },
                    {
                        name: '平均价格',
                        type: 'line',
                        yAxisIndex: 1,
                        data: data.avg_prices,
                        itemStyle: {
                            color: '#91cc75'
                        },
                        smooth: true
                    }
                ]
            };
            myChart.setOption(option);
        });

        window.addEventListener('resize', function() {
            myChart.resize();
        });
</script>
{% endblock %}